
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { useState } from 'react';
import { DispatchControlsProps } from './types';
import { useAppStore } from '@/lib/store';

export function DispatchControls({
  selectedDispatchVouchers,
  selectedDepartment,
  dispatchPerson,
  setDispatchPerson,
  handleDispatchVouchers
}: DispatchControlsProps) {
  const [customName, setCustomName] = useState('');
  const [showCustomInput, setShowCustomInput] = useState(false);
  const currentUser = useAppStore((state) => state.currentUser);

  const getAuditUsers = () => {
    // Get actual audit users from the system
    const allUsers = useAppStore.getState().users || [];
    const auditUsers = allUsers.filter(user => user.department === 'AUDIT' && user.isActive);
    const userNames = auditUsers.map(user => user.name);

    // Always include current user if they're in audit
    if (currentUser && currentUser.department === 'AUDIT' && !userNames.includes(currentUser.name)) {
      userNames.unshift(currentUser.name);
    }

    return [
      "SELECT_PERSON",
      ...userNames,
      "GUEST"
    ];
  };

  // Handle selection change
  const handleSelectionChange = (value: string) => {
    setDispatchPerson(value);
    setShowCustomInput(value === "GUEST");
    if (value !== "GUEST") {
      setCustomName('');
    }
  };

  // Handle custom name change
  const handleCustomNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.toUpperCase();
    setCustomName(value);
    if (value) {
      setDispatchPerson(value); // Update the dispatch person with the custom name
    }
  };

  // Determine if the dispatch button should be disabled
  const isDispatchButtonDisabled = () => {
    if (selectedDispatchVouchers.length === 0 || !selectedDepartment) {
      return true;
    }

    if (dispatchPerson === "SELECT_PERSON") {
      return true;
    }

    // If GUEST is selected, require a custom name
    if (dispatchPerson === "GUEST" && !customName.trim()) {
      return true;
    }

    return false;
  };

  return (
    <div className="flex flex-col space-y-4 mt-6">
      <div className="flex space-x-4">
        <div className="flex flex-col space-y-2">
          <Select value={dispatchPerson} onValueChange={handleSelectionChange}>
            <SelectTrigger className="w-[240px]">
              <SelectValue placeholder="Select dispatcher" />
            </SelectTrigger>
            <SelectContent>
              {getAuditUsers().map((user) => (
                <SelectItem key={user} value={user}>
                  {user === "SELECT_PERSON" ? "SELECT PERSON" : user}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {showCustomInput && (
            <Input
              type="text"
              placeholder="Enter guest name"
              value={customName}
              onChange={handleCustomNameChange}
              className="w-[240px]"
            />
          )}
        </div>

        <Button
          onClick={handleDispatchVouchers}
          disabled={isDispatchButtonDisabled()}
          variant="success"
          className="uppercase"
        >
          DISPATCH VOUCHERS ({selectedDispatchVouchers.length})
        </Button>
      </div>

      {showCustomInput && (
        <div className="flex space-x-4">
          <Input
            type="text"
            placeholder="Enter name"
            value={customName}
            onChange={handleCustomNameChange}
            className="w-[240px] uppercase"
          />
        </div>
      )}
    </div>
  );
}
