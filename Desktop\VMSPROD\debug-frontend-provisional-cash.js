const axios = require('axios');

async function debugFrontendProvisionalCash() {
  try {
    console.log('🔍 Debugging Frontend Provisional Cash Loading...\n');

    // Step 1: Login as audit user (who can see all records)
    console.log('1. Logging in as AUDIT user...');
    const loginResponse = await axios.post('http://localhost:8080/api/auth/login', {
      department: 'AUDIT',
      username: 'SAMUEL ASIEDU',
      password: '123'
    }, {
      withCredentials: true
    });

    const cookies = loginResponse.headers['set-cookie'];
    const cookieHeader = cookies ? cookies.join('; ') : '';
    console.log('✅ Login successful');

    // Step 2: Test the API endpoint that the frontend uses
    console.log('\n2. Testing frontend API endpoint...');
    const frontendApiResponse = await axios.get('http://localhost:8080/api/provisional-cash', {
      headers: {
        'Cookie': cookieHeader
      }
    });

    console.log(`✅ Frontend API returns ${frontendApiResponse.data.length} records`);
    if (frontendApiResponse.data.length > 0) {
      console.log('Records from frontend API:');
      frontendApiResponse.data.forEach((record, index) => {
        console.log(`   ${index + 1}. ${record.voucherRef} - ${record.claimant} - ${record.mainAmount} ${record.currency}`);
      });
    }

    // Step 3: Test vouchers API to check voucher data
    console.log('\n3. Testing vouchers API...');
    const vouchersResponse = await axios.get('http://localhost:8080/api/vouchers', {
      headers: {
        'Cookie': cookieHeader
      }
    });

    console.log(`✅ Vouchers API returns ${vouchersResponse.data.length} vouchers`);
    
    // Check vouchers that have provisional cash records
    const vouchersWithProvisionalCash = vouchersResponse.data.filter(v => v.postProvisionalCash);
    console.log(`✅ Vouchers with provisional cash flag: ${vouchersWithProvisionalCash.length}`);
    
    if (vouchersWithProvisionalCash.length > 0) {
      console.log('Vouchers with provisional cash flag:');
      vouchersWithProvisionalCash.forEach((voucher, index) => {
        console.log(`   ${index + 1}. ${voucher.voucherId} - ${voucher.claimant}`);
        console.log(`      Department: ${voucher.department} | Original: ${voucher.originalDepartment}`);
        console.log(`      Status: ${voucher.status}`);
      });
    }

    // Step 4: Simulate frontend filtering logic
    console.log('\n4. Simulating frontend filtering logic for FINANCE department...');
    
    const provisionalCashRecords = frontendApiResponse.data;
    const vouchers = vouchersResponse.data;
    const department = 'FINANCE';
    
    console.log(`   Total provisional cash records: ${provisionalCashRecords.length}`);
    console.log(`   Total vouchers: ${vouchers.length}`);
    
    const departmentRecords = provisionalCashRecords.filter(record => {
      const voucherId = record.voucherId;
      const voucher = vouchers.find(v => v.id === voucherId);
      
      if (!voucher) {
        console.log(`   ⚠️  No voucher found for record ${record.voucher_ref} (voucherId: ${voucherId})`);
        return false;
      }
      
      const matches = voucher.originalDepartment === department || voucher.department === department;
      console.log(`   📋 Record ${record.voucherRef}: voucher dept=${voucher.department}, original=${voucher.originalDepartment}, matches=${matches}`);
      
      return matches;
    });
    
    console.log(`✅ Filtered records for ${department}: ${departmentRecords.length}`);
    
    if (departmentRecords.length > 0) {
      console.log('Filtered records:');
      departmentRecords.forEach((record, index) => {
        console.log(`   ${index + 1}. ${record.voucherRef} - ${record.claimant} - ${record.mainAmount} ${record.currency}`);
      });
    } else {
      console.log('❌ No records match the filtering criteria');
    }

    // Step 5: Test department-specific API call
    console.log('\n5. Testing department-specific API call...');
    const deptApiResponse = await axios.get(`http://localhost:8080/api/provisional-cash?department=${department}`, {
      headers: {
        'Cookie': cookieHeader
      }
    });

    console.log(`✅ Department API returns ${deptApiResponse.data.length} records for ${department}`);
    if (deptApiResponse.data.length > 0) {
      console.log('Department-specific records:');
      deptApiResponse.data.forEach((record, index) => {
        console.log(`   ${index + 1}. ${record.voucherRef} - ${record.claimant} - ${record.mainAmount} ${record.currency}`);
      });
    }

    console.log('\n🎯 Debug Summary:');
    console.log('='.repeat(60));
    console.log(`📊 Total records in database: ${frontendApiResponse.data.length}`);
    console.log(`📊 Records matching frontend filter: ${departmentRecords.length}`);
    console.log(`📊 Records from department API: ${deptApiResponse.data.length}`);
    console.log(`📊 Vouchers with provisional cash flag: ${vouchersWithProvisionalCash.length}`);

  } catch (error) {
    console.error('❌ Debug Error:', error.response?.data || error.message);
  }
}

debugFrontendProvisionalCash();
