const mysql = require('mysql2/promise');

async function checkDates() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root', 
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  const [vouchers] = await connection.execute('SELECT id, voucher_id, date, dispatch_time, receipt_time FROM vouchers LIMIT 5');
  console.log('Actual database date values:');
  vouchers.forEach(v => {
    console.log(`ID: ${v.id}`);
    console.log(`  date: '${v.date}'`);
    console.log(`  dispatch_time: '${v.dispatch_time}'`);
    console.log(`  receipt_time: '${v.receipt_time}'`);
    console.log('---');
  });
  
  await connection.end();
}

checkDates().catch(console.error);
