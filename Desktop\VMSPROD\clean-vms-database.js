const mysql = require('mysql2/promise');

async function cleanVMSDatabase() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });

  console.log('🧹 CLEANING VMS DATABASE FOR FRESH TESTING');
  console.log('='.repeat(50));

  try {
    // Start transaction for safety
    await connection.beginTransaction();

    // 1. Delete all voucher batches first (foreign key constraints)
    console.log('\n1. Cleaning voucher batches...');
    const [batchResult] = await connection.execute('DELETE FROM voucher_batches');
    console.log(`   ✅ Deleted ${batchResult.affectedRows} voucher batches`);

    // 2. Delete all notifications related to vouchers
    console.log('\n2. Cleaning voucher notifications...');
    const [notificationResult] = await connection.execute(`
      DELETE FROM notifications 
      WHERE type IN ('VOUCHER_CERTIFIED', 'VOUCHER_REJECTED', 'VOUCHER_RETURNED', 'BATCH_RECEIVED', 'BATCH_NOTIFICATION')
    `);
    console.log(`   ✅ Deleted ${notificationResult.affectedRows} voucher notifications`);

    // 3. Delete all vouchers (main table)
    console.log('\n3. Cleaning all vouchers...');
    const [voucherResult] = await connection.execute('DELETE FROM vouchers');
    console.log(`   ✅ Deleted ${voucherResult.affectedRows} vouchers`);

    // 4. Reset auto-increment counters
    console.log('\n4. Resetting auto-increment counters...');
    await connection.execute('ALTER TABLE vouchers AUTO_INCREMENT = 1');
    await connection.execute('ALTER TABLE voucher_batches AUTO_INCREMENT = 1');
    await connection.execute('ALTER TABLE notifications AUTO_INCREMENT = 1');
    console.log('   ✅ Auto-increment counters reset');

    // 5. Verify cleanup
    console.log('\n5. Verifying cleanup...');
    const [voucherCount] = await connection.execute('SELECT COUNT(*) as count FROM vouchers');
    const [batchCount] = await connection.execute('SELECT COUNT(*) as count FROM voucher_batches');
    const [notificationCount] = await connection.execute(`
      SELECT COUNT(*) as count FROM notifications 
      WHERE type IN ('VOUCHER_CERTIFIED', 'VOUCHER_REJECTED', 'VOUCHER_RETURNED', 'BATCH_RECEIVED', 'BATCH_NOTIFICATION')
    `);

    console.log(`   📊 Remaining vouchers: ${voucherCount[0].count}`);
    console.log(`   📊 Remaining voucher batches: ${batchCount[0].count}`);
    console.log(`   📊 Remaining voucher notifications: ${notificationCount[0].count}`);

    // Commit transaction
    await connection.commit();

    console.log('\n✅ VMS DATABASE SUCCESSFULLY CLEANED!');
    console.log('='.repeat(50));
    console.log('🎯 Ready for fresh testing of offset logic');
    console.log('🚀 You can now create new vouchers and test the complete workflow');

  } catch (error) {
    // Rollback on error
    await connection.rollback();
    console.error('❌ Error cleaning database:', error);
    throw error;
  } finally {
    await connection.end();
  }
}

// Run the cleanup
cleanVMSDatabase().catch(console.error);
