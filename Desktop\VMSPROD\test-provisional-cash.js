const mysql = require('mysql2/promise');

async function checkProvisionalCash() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });

    console.log('✅ Connected to database');

    // Check provisional cash records
    const [records] = await connection.execute('SELECT * FROM provisional_cash_records ORDER BY date DESC LIMIT 10');
    console.log('📊 Provisional Cash Records:', records.length);
    
    if (records.length > 0) {
      console.log('Recent records:');
      records.forEach((record, index) => {
        console.log(`${index + 1}. ${record.voucher_ref} - ${record.claimant} - ${record.main_amount} ${record.currency} (${record.date})`);
      });
    } else {
      console.log('❌ No provisional cash records found');
    }

    // Check vouchers with postProvisionalCash flag
    const [vouchers] = await connection.execute('SELECT id, voucher_id, claimant, post_provisional_cash FROM vouchers WHERE post_provisional_cash = TRUE ORDER BY created_at DESC LIMIT 10');
    console.log('📋 Vouchers with provisional cash flag:', vouchers.length);

    if (vouchers.length > 0) {
      console.log('Vouchers marked for provisional cash:');
      vouchers.forEach((voucher, index) => {
        console.log(`${index + 1}. ${voucher.voucher_id} - ${voucher.claimant} - Flag: ${voucher.post_provisional_cash}`);
      });
    }

    // Check recent audit activity with department info
    const [auditVouchers] = await connection.execute(`
      SELECT id, voucher_id, claimant, status, post_provisional_cash, department, original_department, created_at
      FROM vouchers
      WHERE post_provisional_cash = TRUE
      ORDER BY created_at DESC
      LIMIT 10
    `);
    console.log('📋 Vouchers with provisional cash flag:', auditVouchers.length);

    if (auditVouchers.length > 0) {
      console.log('Vouchers with provisional cash (showing department info):');
      auditVouchers.forEach((voucher, index) => {
        console.log(`${index + 1}. ${voucher.voucher_id} - ${voucher.claimant} - Dept: ${voucher.department} - Original: ${voucher.original_department} - Status: ${voucher.status}`);
      });
    }

    // Check provisional cash records with voucher department info
    const [recordsWithDept] = await connection.execute(`
      SELECT pcr.voucher_ref, pcr.claimant, pcr.main_amount, pcr.currency,
             v.department, v.original_department, v.status
      FROM provisional_cash_records pcr
      JOIN vouchers v ON pcr.voucher_id = v.id
      ORDER BY pcr.date DESC
    `);
    console.log('📊 Provisional cash records with department info:', recordsWithDept.length);

    if (recordsWithDept.length > 0) {
      console.log('Records with department details:');
      recordsWithDept.forEach((record, index) => {
        console.log(`${index + 1}. ${record.voucher_ref} - ${record.claimant} - Dept: ${record.department} - Original: ${record.original_department} - Status: ${record.status}`);
      });
    }

    await connection.end();
  } catch (error) {
    console.error('❌ Database error:', error.message);
  }
}

checkProvisionalCash();
