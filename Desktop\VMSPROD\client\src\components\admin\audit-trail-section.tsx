import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Shield, 
  Activity, 
  Users, 
  AlertTriangle, 
  Info, 
  Search,
  Filter,
  RefreshCw,
  Clock,
  User,
  Building
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface AuditLog {
  id: string;
  timestamp: string;
  user_name: string;
  department: string;
  action: string;
  description: string;
  resource_type: string;
  severity: 'INFO' | 'WARNING' | 'ERROR';
  ip_address: string;
}

interface AuditStats {
  total_activities: number;
  active_users: number;
  logins: number;
  vouchers_created: number;
  vouchers_certified: number;
  errors: number;
  warnings: number;
}

export function AuditTrailSection() {
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [auditStats, setAuditStats] = useState<AuditStats | null>(null);
  const [recentActivities, setRecentActivities] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasAccess, setHasAccess] = useState(true);
  
  // Filters
  const [filters, setFilters] = useState({
    department: '',
    action: '',
    severity: '',
    search: '',
    timeframe: 'today'
  });

  useEffect(() => {
    loadAuditData();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(loadRecentActivities, 30000);
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    loadAuditLogs();
  }, [filters]);

  const loadAuditData = async () => {
    await Promise.all([
      loadAuditStats(),
      loadRecentActivities(),
      loadAuditLogs()
    ]);
  };

  const loadAuditStats = async () => {
    try {
      const response = await fetch(`/api/audit-trail/stats?timeframe=${filters.timeframe}`, {
        credentials: 'include'
      });

      if (response.ok) {
        const stats = await response.json();
        setAuditStats(stats);
      } else if (response.status === 403) {
        setHasAccess(false);
      }
    } catch (error) {
      console.error('Error loading audit stats:', error);
      setHasAccess(false);
    }
  };

  const loadRecentActivities = async () => {
    try {
      const response = await fetch('/api/audit-trail/recent?limit=10', {
        credentials: 'include'
      });

      if (response.ok) {
        const activities = await response.json();
        setRecentActivities(activities);
      } else if (response.status === 403) {
        setHasAccess(false);
      }
    } catch (error) {
      console.error('Error loading recent activities:', error);
      setHasAccess(false);
    }
  };

  const loadAuditLogs = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (filters.department) params.append('department', filters.department);
      if (filters.action) params.append('action', filters.action);
      if (filters.severity) params.append('severity', filters.severity);
      params.append('limit', '50');

      const response = await fetch(`/api/audit-trail/logs?${params}`, {
        credentials: 'include'
      });

      if (response.ok) {
        const logs = await response.json();
        setAuditLogs(logs);
      } else if (response.status === 403) {
        setHasAccess(false);
      }
    } catch (error) {
      console.error('Error loading audit logs:', error);
      setHasAccess(false);
    } finally {
      setLoading(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'ERROR': return 'bg-red-100 text-red-800 border-red-200';
      case 'WARNING': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default: return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'ERROR': return <AlertTriangle className="h-3 w-3" />;
      case 'WARNING': return <AlertTriangle className="h-3 w-3" />;
      default: return <Info className="h-3 w-3" />;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true
    });
  };

  const filteredLogs = auditLogs.filter(log => {
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      return log.description.toLowerCase().includes(searchLower) ||
             log.user_name.toLowerCase().includes(searchLower);
    }
    return true;
  });

  // Show access denied message for non-admin users
  if (!hasAccess) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-2">
          <Shield className="h-6 w-6 text-blue-600" />
          <h2 className="text-2xl font-bold">Audit Trail</h2>
        </div>

        <Card>
          <CardContent className="p-12">
            <div className="text-center">
              <Shield className="h-16 w-16 mx-auto mb-4 text-gray-400" />
              <h3 className="text-xl font-semibold mb-2">Admin Access Required</h3>
              <p className="text-gray-600 mb-4">
                The audit trail feature is only available to system administrators.
              </p>
              <p className="text-sm text-gray-500">
                Contact your system administrator if you need access to audit logs.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Shield className="h-6 w-6 text-blue-600" />
          <h2 className="text-2xl font-bold">Audit Trail</h2>
        </div>
        <Button onClick={loadAuditData} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Statistics Cards */}
      {auditStats && (
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Activities</p>
                  <p className="text-2xl font-bold">{auditStats.total_activities}</p>
                </div>
                <Activity className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Active Users</p>
                  <p className="text-2xl font-bold">{auditStats.active_users}</p>
                </div>
                <Users className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Logins</p>
                  <p className="text-2xl font-bold">{auditStats.logins}</p>
                </div>
                <User className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Vouchers Created</p>
                  <p className="text-2xl font-bold">{auditStats.vouchers_created}</p>
                </div>
                <Building className="h-8 w-8 text-indigo-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Vouchers Certified</p>
                  <p className="text-2xl font-bold">{auditStats.vouchers_certified}</p>
                </div>
                <Shield className="h-8 w-8 text-teal-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Warnings</p>
                  <p className="text-2xl font-bold text-yellow-600">{auditStats.warnings}</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Errors</p>
                  <p className="text-2xl font-bold text-red-600">{auditStats.errors}</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Activities Live Feed */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="mr-2 h-5 w-5" />
              Live Activity Feed
            </CardTitle>
            <CardDescription>Real-time user activities</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                  <div className={`p-1 rounded-full ${getSeverityColor(activity.severity)}`}>
                    {getSeverityIcon(activity.severity)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {activity.description}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatTimestamp(activity.timestamp)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Audit Logs */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Filter className="mr-2 h-5 w-5" />
              Audit Logs
            </CardTitle>
            <CardDescription>Detailed activity history with filtering</CardDescription>
          </CardHeader>
          <CardContent>
            {/* Filters */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search activities..."
                  value={filters.search}
                  onChange={(e) => setFilters({...filters, search: e.target.value})}
                  className="pl-10"
                />
              </div>

              <Select value={filters.department} onValueChange={(value) => setFilters({...filters, department: value})}>
                <SelectTrigger>
                  <SelectValue placeholder="Department" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Departments</SelectItem>
                  <SelectItem value="FINANCE">Finance</SelectItem>
                  <SelectItem value="AUDIT">Audit</SelectItem>
                  <SelectItem value="SYSTEM ADMIN">System Admin</SelectItem>
                </SelectContent>
              </Select>

              <Select value={filters.severity} onValueChange={(value) => setFilters({...filters, severity: value})}>
                <SelectTrigger>
                  <SelectValue placeholder="Severity" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Severities</SelectItem>
                  <SelectItem value="INFO">Info</SelectItem>
                  <SelectItem value="WARNING">Warning</SelectItem>
                  <SelectItem value="ERROR">Error</SelectItem>
                </SelectContent>
              </Select>

              <Select value={filters.timeframe} onValueChange={(value) => setFilters({...filters, timeframe: value})}>
                <SelectTrigger>
                  <SelectValue placeholder="Timeframe" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="week">This Week</SelectItem>
                  <SelectItem value="month">This Month</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Logs Table */}
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {loading ? (
                <div className="text-center py-8">
                  <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
                  <p>Loading audit logs...</p>
                </div>
              ) : filteredLogs.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Shield className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No audit logs found</p>
                </div>
              ) : (
                filteredLogs.map((log) => (
                  <div key={log.id} className="border rounded-lg p-4 hover:bg-gray-50">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <Badge className={getSeverityColor(log.severity)}>
                            {log.severity}
                          </Badge>
                          <Badge variant="outline">{log.department}</Badge>
                          <span className="text-sm text-gray-500">{log.user_name}</span>
                        </div>
                        <p className="text-sm font-medium text-gray-900 mb-1">
                          {log.description}
                        </p>
                        <p className="text-xs text-gray-500">
                          {formatTimestamp(log.timestamp)} • {log.ip_address}
                        </p>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
