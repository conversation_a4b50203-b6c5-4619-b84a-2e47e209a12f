"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.apiRouter = void 0;
const express_1 = __importDefault(require("express"));
const auth_js_1 = require("./auth.js");
const users_js_1 = require("./users.js");
const vouchers_js_1 = require("./vouchers.js");
const batches_js_1 = require("./batches.js");
const provisionalCash_js_1 = require("./provisionalCash.js");
const notifications_js_1 = require("./notifications.js");
const admin_js_1 = require("./admin.js");
const audit_js_1 = require("./audit.js");
const years_js_1 = require("./years.js");
const pendrive_backup_js_1 = require("./pendrive-backup.js");
const audit_trail_js_1 = require("./audit-trail.js");
const auth_js_2 = require("../middleware/auth.js");
const apiRouter = express_1.default.Router();
exports.apiRouter = apiRouter;
// Mount all routes
apiRouter.use('/auth', auth_js_1.authRouter);
apiRouter.use('/users', users_js_1.userRouter);
apiRouter.use('/vouchers', vouchers_js_1.voucherRouter);
apiRouter.use('/batches', batches_js_1.batchRouter);
apiRouter.use('/provisional-cash', provisionalCash_js_1.provisionalCashRouter);
apiRouter.use('/notifications', notifications_js_1.notificationRouter);
apiRouter.use('/admin', admin_js_1.adminRouter);
apiRouter.use('/audit', audit_js_1.auditRouter);
apiRouter.use('/years', years_js_1.yearRouter);
apiRouter.use('/pendrive-backup', pendrive_backup_js_1.pendriveBackupRouter);
apiRouter.use('/audit-trail', audit_trail_js_1.auditTrailRouter);
// API version and status endpoint
apiRouter.get('/', (req, res) => {
    res.json({
        name: 'Voucher Management System API',
        version: '1.0.0',
        status: 'active'
    });
});
// Health endpoint for service discovery
apiRouter.get('/health', (req, res) => {
    const healthData = {
        status: 'healthy',
        service: 'vms-server',
        serviceName: 'VMS-Server',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: '3.0.0',
        environment: process.env.NODE_ENV || 'production',
        pid: process.pid
    };
    res.json(healthData);
});
// Basic system info endpoint (accessible to all authenticated users)
apiRouter.get('/system-info', auth_js_2.authenticate, async (req, res) => {
    try {
        const { query } = await import('../database/db.js');
        // Get basic system information that's safe for all users
        const activeUsers = await query(`
      SELECT COUNT(DISTINCT user_id) as count
      FROM active_sessions
      WHERE is_active = TRUE
      AND last_activity >= DATE_SUB(NOW(), INTERVAL 30 MINUTE)
    `);
        const totalSessions = await query(`
      SELECT COUNT(*) as count
      FROM active_sessions
      WHERE is_active = TRUE
    `);
        const systemInfo = {
            activeUsers: parseInt(activeUsers[0]?.count || '0'),
            totalSessions: parseInt(totalSessions[0]?.count || '0'),
            serverTime: new Date().toISOString(),
            uptime: process.uptime(),
            status: 'healthy'
        };
        res.json(systemInfo);
    }
    catch (error) {
        const { logger } = await import('../utils/logger.js');
        logger.error('System info error:', error);
        res.status(500).json({ error: 'Failed to get system info' });
    }
});
// Basic analytics endpoint (accessible to all authenticated users)
apiRouter.get('/basic-analytics', auth_js_2.authenticate, async (req, res) => {
    try {
        const { query } = await import('../database/db.js');
        const timeframe = req.query.timeframe || 'week';
        let dateCondition = '';
        switch (timeframe) {
            case 'today':
                dateCondition = 'DATE(created_at) = CURDATE()';
                break;
            case 'week':
                dateCondition = 'created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
                break;
            case 'month':
                dateCondition = 'created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
                break;
            default:
                dateCondition = 'created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
        }
        // Get voucher metrics
        const voucherMetrics = await query(`
      SELECT
        COUNT(*) as total_vouchers,
        SUM(CASE WHEN status = 'PENDING_DISPATCH' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'DISPATCHED' THEN 1 ELSE 0 END) as dispatched,
        SUM(CASE WHEN status = 'CERTIFIED' THEN 1 ELSE 0 END) as certified,
        SUM(amount) as total_amount,
        AVG(amount) as average_amount
      FROM vouchers
      WHERE ${dateCondition}
    `);
        // Get department activity
        const departmentActivity = await query(`
      SELECT
        department,
        COUNT(*) as voucher_count,
        SUM(amount) as total_amount
      FROM vouchers
      WHERE ${dateCondition}
      GROUP BY department
      ORDER BY voucher_count DESC
    `);
        // Get daily activity (for charts)
        const dailyActivity = await query(`
      SELECT
        DATE(created_at) as date,
        COUNT(*) as voucher_count,
        SUM(amount) as total_amount
      FROM vouchers
      WHERE ${dateCondition}
      GROUP BY DATE(created_at)
      ORDER BY date
    `);
        const analytics = {
            voucherMetrics: voucherMetrics[0] || {
                total_vouchers: 0,
                pending: 0,
                dispatched: 0,
                certified: 0,
                total_amount: 0,
                average_amount: 0
            },
            departmentActivity,
            dailyActivity,
            timeframe,
            generatedAt: new Date().toISOString()
        };
        res.json(analytics);
    }
    catch (error) {
        const { logger } = await import('../utils/logger.js');
        logger.error('Basic analytics error:', error);
        res.status(500).json({ error: 'Failed to get analytics data' });
    }
});
//# sourceMappingURL=index.js.map