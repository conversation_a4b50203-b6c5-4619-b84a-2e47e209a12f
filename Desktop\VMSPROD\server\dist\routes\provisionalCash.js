"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.provisionalCashRouter = void 0;
const express_1 = __importDefault(require("express"));
const uuid_1 = require("uuid");
const db_js_1 = require("../database/db.js");
const auth_js_1 = require("../middleware/auth.js");
const logger_js_1 = require("../utils/logger.js");
exports.provisionalCashRouter = express_1.default.Router();
// Apply authentication middleware to all routes
exports.provisionalCashRouter.use(auth_js_1.authenticate);
// Get all provisional cash records
exports.provisionalCashRouter.get('/', async (req, res) => {
    try {
        const { department } = req.query;
        let records;
        if (department) {
            // Get vouchers for the specified department - use original_department to show records from vouchers that originated from this department
            const vouchers = await (0, db_js_1.query)('SELECT id FROM vouchers WHERE original_department = ? OR department = ?', [department, department]);
            const voucherIds = vouchers.map((v) => v.id);
            if (voucherIds.length === 0) {
                return res.json([]);
            }
            // Get provisional cash records for these vouchers
            records = await (0, db_js_1.query)(`SELECT * FROM provisional_cash_records WHERE voucher_id IN (${voucherIds.map(() => '?').join(',')})`, voucherIds);
        }
        else if (req.user.department === 'AUDIT' || req.user.department === 'SYSTEM ADMIN') {
            // Audit and Admin see all records
            records = await (0, db_js_1.query)('SELECT * FROM provisional_cash_records');
        }
        else {
            // Other departments see only their records - use original_department to show records from vouchers that originated from this department
            const vouchers = await (0, db_js_1.query)('SELECT id FROM vouchers WHERE original_department = ? OR department = ?', [req.user.department, req.user.department]);
            const voucherIds = vouchers.map((v) => v.id);
            if (voucherIds.length === 0) {
                return res.json([]);
            }
            // Get provisional cash records for these vouchers
            records = await (0, db_js_1.query)(`SELECT * FROM provisional_cash_records WHERE voucher_id IN (${voucherIds.map(() => '?').join(',')})`, voucherIds);
        }
        // Convert snake_case field names to camelCase for frontend
        const convertedRecords = records.map((record) => ({
            id: record.id,
            voucherId: record.voucher_id,
            voucherRef: record.voucher_ref,
            claimant: record.claimant,
            description: record.description,
            mainAmount: record.main_amount,
            currency: record.currency,
            amountRetired: record.amount_retired,
            clearanceRemark: record.clearance_remark,
            dateRetired: record.date_retired,
            clearedBy: record.cleared_by,
            comment: record.comment,
            date: record.date
        }));
        res.json(convertedRecords);
    }
    catch (error) {
        logger_js_1.logger.error('Get provisional cash records error:', error);
        res.status(500).json({ error: 'Failed to get provisional cash records' });
    }
});
// Get provisional cash record by ID
exports.provisionalCashRouter.get('/:id', async (req, res) => {
    try {
        const recordId = req.params.id;
        // Get record
        const records = await (0, db_js_1.query)('SELECT * FROM provisional_cash_records WHERE id = ?', [recordId]);
        if (records.length === 0) {
            return res.status(404).json({ error: 'Provisional cash record not found' });
        }
        const record = records[0];
        // Get voucher to check department
        const vouchers = await (0, db_js_1.query)('SELECT * FROM vouchers WHERE id = ?', [record.voucher_id]);
        if (vouchers.length === 0) {
            return res.status(404).json({ error: 'Associated voucher not found' });
        }
        const voucher = vouchers[0];
        // Check if user has access to this record
        if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN' && voucher.department !== req.user.department) {
            return res.status(403).json({ error: 'Access denied' });
        }
        res.json(record);
    }
    catch (error) {
        logger_js_1.logger.error('Get provisional cash record error:', error);
        res.status(500).json({ error: 'Failed to get provisional cash record' });
    }
});
// Create provisional cash record
exports.provisionalCashRouter.post('/', async (req, res) => {
    try {
        const { voucherId, voucherRef, claimant, description, mainAmount, currency, date } = req.body;
        // Validate required fields
        if (!voucherId || !voucherRef || !claimant || !description || !mainAmount || !currency || !date) {
            return res.status(400).json({ error: 'Missing required fields' });
        }
        // Check if voucher exists
        const vouchers = await (0, db_js_1.query)('SELECT * FROM vouchers WHERE id = ?', [voucherId]);
        if (vouchers.length === 0) {
            return res.status(404).json({ error: 'Voucher not found' });
        }
        const voucher = vouchers[0];
        // Check if user has access to create records for this voucher
        if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN') {
            return res.status(403).json({ error: 'Access denied' });
        }
        // Create record
        const id = (0, uuid_1.v4)();
        await (0, db_js_1.query)(`INSERT INTO provisional_cash_records (
        id, voucher_id, voucher_ref, claimant, description, main_amount, currency, date
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [id, voucherId, voucherRef, claimant, description, mainAmount, currency, date]);
        // Update voucher
        await (0, db_js_1.query)('UPDATE vouchers SET post_provisional_cash = TRUE WHERE id = ?', [voucherId]);
        // Get created record
        const records = await (0, db_js_1.query)('SELECT * FROM provisional_cash_records WHERE id = ?', [id]);
        res.status(201).json(records[0]);
    }
    catch (error) {
        logger_js_1.logger.error('Create provisional cash record error:', error);
        res.status(500).json({ error: 'Failed to create provisional cash record' });
    }
});
// Update provisional cash record
exports.provisionalCashRouter.put('/:id', async (req, res) => {
    try {
        const recordId = req.params.id;
        // Get record
        const records = await (0, db_js_1.query)('SELECT * FROM provisional_cash_records WHERE id = ?', [recordId]);
        if (records.length === 0) {
            return res.status(404).json({ error: 'Provisional cash record not found' });
        }
        const record = records[0];
        // Get voucher to check department
        const vouchers = await (0, db_js_1.query)('SELECT * FROM vouchers WHERE id = ?', [record.voucher_id]);
        if (vouchers.length === 0) {
            return res.status(404).json({ error: 'Associated voucher not found' });
        }
        const voucher = vouchers[0];
        // Check if user has access to update this record
        if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN') {
            return res.status(403).json({ error: 'Access denied' });
        }
        // Build update query
        let updateQuery = 'UPDATE provisional_cash_records SET ';
        const updateParams = [];
        const updates = [];
        // Process each field in the request body
        for (const [key, value] of Object.entries(req.body)) {
            // Convert camelCase to snake_case for database column names
            const columnName = key.replace(/([A-Z])/g, '_$1').toLowerCase();
            updates.push(`${columnName} = ?`);
            updateParams.push(value);
        }
        // If no updates, return early
        if (updates.length === 0) {
            return res.status(400).json({ error: 'No updates provided' });
        }
        updateQuery += updates.join(', ') + ' WHERE id = ?';
        updateParams.push(recordId);
        // Execute update
        await (0, db_js_1.query)(updateQuery, updateParams);
        // Get updated record
        const updatedRecords = await (0, db_js_1.query)('SELECT * FROM provisional_cash_records WHERE id = ?', [recordId]);
        res.json(updatedRecords[0]);
    }
    catch (error) {
        logger_js_1.logger.error('Update provisional cash record error:', error);
        res.status(500).json({ error: 'Failed to update provisional cash record' });
    }
});
// Delete provisional cash record
exports.provisionalCashRouter.delete('/:id', async (req, res) => {
    try {
        const recordId = req.params.id;
        // Get record
        const records = await (0, db_js_1.query)('SELECT * FROM provisional_cash_records WHERE id = ?', [recordId]);
        if (records.length === 0) {
            return res.status(404).json({ error: 'Provisional cash record not found' });
        }
        const record = records[0];
        // Get voucher to check department
        const vouchers = await (0, db_js_1.query)('SELECT * FROM vouchers WHERE id = ?', [record.voucher_id]);
        if (vouchers.length === 0) {
            return res.status(404).json({ error: 'Associated voucher not found' });
        }
        const voucher = vouchers[0];
        // Check if user has access to delete this record
        if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN') {
            return res.status(403).json({ error: 'Access denied' });
        }
        // Delete record
        await (0, db_js_1.query)('DELETE FROM provisional_cash_records WHERE id = ?', [recordId]);
        // Update voucher if no more records exist for it
        const remainingRecords = await (0, db_js_1.query)('SELECT COUNT(*) as count FROM provisional_cash_records WHERE voucher_id = ?', [record.voucher_id]);
        if (remainingRecords[0].count === 0) {
            await (0, db_js_1.query)('UPDATE vouchers SET post_provisional_cash = FALSE WHERE id = ?', [record.voucher_id]);
        }
        res.json({ message: 'Provisional cash record deleted successfully' });
    }
    catch (error) {
        logger_js_1.logger.error('Delete provisional cash record error:', error);
        res.status(500).json({ error: 'Failed to delete provisional cash record' });
    }
});
//# sourceMappingURL=provisionalCash.js.map