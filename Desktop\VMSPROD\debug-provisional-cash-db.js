const mysql = require('mysql2/promise');

async function debugProvisionalCashDB() {
  let connection;
  try {
    console.log('🔄 Connecting to database...');
    
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });

    console.log('✅ Connected to database');

    // Check the actual database structure and data
    console.log('\n📊 Checking provisional_cash_records table structure...');
    const [columns] = await connection.execute('DESCRIBE provisional_cash_records');
    console.log('Table columns:');
    columns.forEach(col => {
      console.log(`- ${col.Field} (${col.Type})`);
    });

    console.log('\n📊 Checking actual data in database...');
    const [records] = await connection.execute('SELECT * FROM provisional_cash_records');
    
    console.log(`Found ${records.length} records:`);
    records.forEach((record, index) => {
      console.log(`\n${index + 1}. Record ID: ${record.id}`);
      console.log(`   Voucher Ref: ${record.voucher_ref}`);
      console.log(`   Claimant: ${record.claimant}`);
      console.log(`   Main Amount: ${record.main_amount}`);
      console.log(`   Currency: ${record.currency}`);
      console.log(`   Amount Retired: ${record.amount_retired}`);
      console.log(`   Clearance Remark: ${record.clearance_remark}`);
      console.log(`   Date Retired: ${record.date_retired}`);
      console.log(`   Cleared By: ${record.cleared_by}`);
      console.log(`   Comment: ${record.comment}`);
    });

    // Find ADDY SABAH record specifically
    const [addyRecords] = await connection.execute(
      'SELECT * FROM provisional_cash_records WHERE claimant LIKE ?', 
      ['%ADDY SABAH%']
    );

    if (addyRecords.length > 0) {
      console.log('\n🎯 ADDY SABAH record details:');
      const record = addyRecords[0];
      console.log('Raw database record:');
      console.log(JSON.stringify(record, null, 2));
    }

  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

debugProvisionalCashDB();
