const axios = require('axios');

async function testProvisionalCashAPI() {
  try {
    console.log('🔄 Testing Provisional Cash API...');

    // First, login to get session
    const loginResponse = await axios.post('http://localhost:8080/api/auth/login', {
      department: 'AUDIT',
      username: 'SAMUEL ASIEDU',
      password: '123'
    }, {
      withCredentials: true
    });

    console.log('✅ Login successful');

    // Extract cookies from login response
    const cookies = loginResponse.headers['set-cookie'];
    const cookieHeader = cookies ? cookies.join('; ') : '';

    // Test provisional cash API
    const provisionalCashResponse = await axios.get('http://localhost:8080/api/provisional-cash', {
      headers: {
        'Cookie': cookieHeader
      }
    });

    console.log('📊 Provisional Cash API Response:');
    console.log('Status:', provisionalCashResponse.status);
    console.log('Records count:', provisionalCashResponse.data.length);
    
    if (provisionalCashResponse.data.length > 0) {
      console.log('Records:');
      provisionalCashResponse.data.forEach((record, index) => {
        console.log(`${index + 1}. ${record.voucher_ref} - ${record.claimant} - ${record.main_amount} ${record.currency}`);
      });
    } else {
      console.log('❌ No records returned from API');
    }

    // Test with department filter
    const financeResponse = await axios.get('http://localhost:8080/api/provisional-cash?department=FINANCE', {
      headers: {
        'Cookie': cookieHeader
      }
    });

    console.log('📊 Finance Department Filter:');
    console.log('Status:', financeResponse.status);
    console.log('Records count:', financeResponse.data.length);

  } catch (error) {
    console.error('❌ API Test Error:', error.response?.data || error.message);
  }
}

testProvisionalCashAPI();
