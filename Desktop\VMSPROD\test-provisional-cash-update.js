const axios = require('axios');

async function testProvisionalCashUpdate() {
  try {
    console.log('🔄 Testing Provisional Cash Update API...');

    // First, login to get session
    const loginResponse = await axios.post('http://localhost:8080/api/auth/login', {
      department: 'AUDIT',
      username: 'SAMUEL ASIEDU',
      password: '123'
    }, {
      withCredentials: true
    });

    console.log('✅ Login successful');

    // Extract cookies from login response
    const cookies = loginResponse.headers['set-cookie'];
    const cookieHeader = cookies ? cookies.join('; ') : '';

    // Get all provisional cash records first
    const getResponse = await axios.get('http://localhost:8080/api/provisional-cash', {
      headers: {
        'Cookie': cookieHeader
      }
    });

    console.log('📊 Current Provisional Cash Records:');
    console.log('Records count:', getResponse.data.length);
    
    if (getResponse.data.length > 0) {
      console.log('Records:');
      getResponse.data.forEach((record, index) => {
        console.log(`${index + 1}. ID: ${record.id}`);
        console.log(`   Voucher: ${record.voucher_ref} - ${record.claimant}`);
        console.log(`   Amount: ${record.main_amount} ${record.currency}`);
        console.log(`   Retired: ${record.amount_retired || 'Not set'}`);
        console.log(`   Status: ${record.clearance_remark || 'Not set'}`);
        console.log('');
      });

      // Find ADDY SABAH record
      const addySabahRecord = getResponse.data.find(record => 
        record.claimant && record.claimant.toUpperCase().includes('ADDY SABAH')
      );

      if (addySabahRecord) {
        console.log('🎯 Found ADDY SABAH record:', addySabahRecord.id);
        console.log('Current amount retired:', addySabahRecord.amount_retired);

        // Test updating this record
        const updateData = {
          amountRetired: 25000.50,
          clearanceRemark: 'DUE STAFF',
          dateRetired: new Date().toISOString().split('T')[0],
          clearedBy: 'SAMUEL ASIEDU',
          comment: 'Test update from API'
        };

        console.log('🔄 Attempting to update record...');
        console.log('Update data:', updateData);

        const updateResponse = await axios.put(`http://localhost:8080/api/provisional-cash/${addySabahRecord.id}`, updateData, {
          headers: {
            'Cookie': cookieHeader,
            'Content-Type': 'application/json'
          }
        });

        console.log('✅ Update successful!');
        console.log('Response status:', updateResponse.status);
        console.log('Updated record:', updateResponse.data);

        // Verify the update by fetching again
        const verifyResponse = await axios.get('http://localhost:8080/api/provisional-cash', {
          headers: {
            'Cookie': cookieHeader
          }
        });

        const updatedRecord = verifyResponse.data.find(record => record.id === addySabahRecord.id);
        console.log('🔍 Verification - Updated record from database:');
        console.log('Amount retired:', updatedRecord.amount_retired);
        console.log('Clearance remark:', updatedRecord.clearance_remark);
        console.log('Date retired:', updatedRecord.date_retired);
        console.log('Cleared by:', updatedRecord.cleared_by);
        console.log('Comment:', updatedRecord.comment);

      } else {
        console.log('❌ ADDY SABAH record not found');
        console.log('Available claimants:');
        getResponse.data.forEach(record => {
          console.log(`- ${record.claimant}`);
        });
      }
    } else {
      console.log('❌ No provisional cash records found');
    }

  } catch (error) {
    console.error('❌ API Test Error:');
    console.error('Status:', error.response?.status);
    console.error('Data:', error.response?.data);
    console.error('Message:', error.message);
  }
}

testProvisionalCashUpdate();
