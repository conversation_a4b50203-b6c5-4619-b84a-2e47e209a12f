const axios = require('axios');

async function testProvisionalCashWorkflow() {
  try {
    console.log('🧪 Testing Complete Provisional Cash Workflow...\n');

    // Step 1: Login as audit user
    console.log('1. Logging in as AUDIT user...');
    const loginResponse = await axios.post('http://localhost:8080/api/auth/login', {
      department: 'AUDIT',
      username: 'SAMUEL ASIEDU',
      password: '123'
    }, {
      withCredentials: true
    });

    console.log('✅ Login successful');

    // Extract cookies from login response
    const cookies = loginResponse.headers['set-cookie'];
    const cookieHeader = cookies ? cookies.join('; ') : '';

    // Step 2: Test provisional cash API (all records)
    console.log('\n2. Testing provisional cash API (all records)...');
    const allRecordsResponse = await axios.get('http://localhost:8080/api/provisional-cash', {
      headers: {
        'Cookie': cookieHeader
      }
    });

    console.log(`✅ Total provisional cash records: ${allRecordsResponse.data.length}`);
    if (allRecordsResponse.data.length > 0) {
      console.log('All records:');
      allRecordsResponse.data.forEach((record, index) => {
        console.log(`   ${index + 1}. ${record.voucher_ref} - ${record.claimant} - ${record.main_amount} ${record.currency}`);
      });
    }

    // Step 3: Test Finance department filter
    console.log('\n3. Testing Finance department filter...');
    const financeRecordsResponse = await axios.get('http://localhost:8080/api/provisional-cash?department=FINANCE', {
      headers: {
        'Cookie': cookieHeader
      }
    });

    console.log(`✅ Finance department records: ${financeRecordsResponse.data.length}`);
    if (financeRecordsResponse.data.length > 0) {
      console.log('Finance records:');
      financeRecordsResponse.data.forEach((record, index) => {
        console.log(`   ${index + 1}. ${record.voucher_ref} - ${record.claimant} - ${record.main_amount} ${record.currency}`);
      });
    }

    // Step 4: Check vouchers with provisional cash flag
    console.log('\n4. Testing vouchers API to check provisional cash flags...');
    const vouchersResponse = await axios.get('http://localhost:8080/api/vouchers', {
      headers: {
        'Cookie': cookieHeader
      }
    });

    const vouchersWithProvisionalCash = vouchersResponse.data.filter(v => v.postProvisionalCash);
    console.log(`✅ Vouchers with provisional cash flag: ${vouchersWithProvisionalCash.length}`);
    
    if (vouchersWithProvisionalCash.length > 0) {
      console.log('Vouchers marked for provisional cash:');
      vouchersWithProvisionalCash.forEach((voucher, index) => {
        console.log(`   ${index + 1}. ${voucher.voucherId} - ${voucher.claimant} - Dept: ${voucher.department} - Original: ${voucher.originalDepartment}`);
      });
    }

    // Step 5: Test creating a new provisional cash record
    console.log('\n5. Testing provisional cash record creation...');
    
    // Find a voucher that doesn't have a provisional cash record yet
    const vouchersWithoutRecords = vouchersWithProvisionalCash.filter(voucher => {
      return !allRecordsResponse.data.some(record => record.voucher_ref === voucher.voucherId);
    });

    if (vouchersWithoutRecords.length > 0) {
      const testVoucher = vouchersWithoutRecords[0];
      console.log(`   Creating record for voucher: ${testVoucher.voucherId} - ${testVoucher.claimant}`);
      
      try {
        const createResponse = await axios.post('http://localhost:8080/api/provisional-cash', {
          voucherId: testVoucher.id,
          voucherRef: testVoucher.voucherId,
          claimant: testVoucher.claimant,
          description: testVoucher.description,
          mainAmount: testVoucher.preAuditedAmount || testVoucher.amount,
          currency: testVoucher.currency,
          date: new Date().toISOString().split('T')[0].replace(/-/g, '-') + '_' + new Date().toTimeString().split(' ')[0].replace(/:/g, '-')
        }, {
          headers: {
            'Cookie': cookieHeader,
            'Content-Type': 'application/json'
          }
        });

        console.log('✅ Provisional cash record created successfully');
        console.log(`   Record ID: ${createResponse.data.id}`);
      } catch (createError) {
        console.log('❌ Failed to create provisional cash record:', createError.response?.data || createError.message);
      }
    } else {
      console.log('   ℹ️  All vouchers with provisional cash flag already have records');
    }

    // Step 6: Final verification
    console.log('\n6. Final verification - checking updated records...');
    const finalRecordsResponse = await axios.get('http://localhost:8080/api/provisional-cash', {
      headers: {
        'Cookie': cookieHeader
      }
    });

    console.log(`✅ Final total records: ${finalRecordsResponse.data.length}`);
    
    const finalFinanceRecords = await axios.get('http://localhost:8080/api/provisional-cash?department=FINANCE', {
      headers: {
        'Cookie': cookieHeader
      }
    });

    console.log(`✅ Final Finance department records: ${finalFinanceRecords.data.length}`);

    console.log('\n🎉 Provisional Cash Workflow Test Complete!');
    console.log('='.repeat(60));
    console.log(`📊 Summary:`);
    console.log(`   • Total provisional cash records: ${finalRecordsResponse.data.length}`);
    console.log(`   • Finance department records: ${finalFinanceRecords.data.length}`);
    console.log(`   • Vouchers with provisional cash flag: ${vouchersWithProvisionalCash.length}`);

  } catch (error) {
    console.error('❌ Workflow Test Error:', error.response?.data || error.message);
  }
}

testProvisionalCashWorkflow();
