const axios = require('axios');

async function testOnlinePresenceSystem() {
  console.log('🧪 TESTING ONLINE PRESENCE SYSTEM');
  console.log('='.repeat(60));

  const baseURL = 'http://localhost:8080';

  try {
    // Step 1: Test server health
    console.log('\n1. SERVER HEALTH CHECK');
    console.log('-'.repeat(40));
    
    const healthResponse = await axios.get(`${baseURL}/api/health`);
    console.log('✅ Server Status:', healthResponse.data.status);
    console.log('📊 Version:', healthResponse.data.version);

    // Step 2: Test authentication
    console.log('\n2. AUTHENTICATION TEST');
    console.log('-'.repeat(40));
    
    const loginResponse = await axios.post(`${baseURL}/api/auth/login`, {
      department: 'FINANCE',
      username: 'FELIX AYISI',
      password: '123'
    }, { withCredentials: true });

    const cookies = loginResponse.headers['set-cookie'];
    const sessionCookie = cookies ? cookies.find(cookie => cookie.startsWith('vms_session_id=')) : null;
    
    console.log('✅ Login successful');
    console.log('👤 User:', loginResponse.data.user.name, '(' + loginResponse.data.user.department + ')');

    // Step 3: Test online users API endpoint
    console.log('\n3. ONLINE USERS API TEST');
    console.log('-'.repeat(40));
    
    try {
      const onlineUsersResponse = await axios.get(`${baseURL}/api/users/online?department=FINANCE`, {
        withCredentials: true,
        headers: sessionCookie ? { 'Cookie': sessionCookie } : {}
      });
      
      console.log('✅ Online users API working');
      console.log('📊 Online users in FINANCE:', onlineUsersResponse.data.length);
      
      if (onlineUsersResponse.data.length > 0) {
        console.log('👥 Online users:');
        onlineUsersResponse.data.forEach(user => {
          const lastName = user.name.split(' ').pop();
          console.log(`   - ${user.name} (Last name: ${lastName})`);
        });
      }
    } catch (apiError) {
      console.log('❌ Online users API failed:', apiError.response?.status || apiError.message);
    }

    // Step 4: Test audit department online users
    console.log('\n4. AUDIT DEPARTMENT ONLINE USERS TEST');
    console.log('-'.repeat(40));
    
    try {
      const auditUsersResponse = await axios.get(`${baseURL}/api/users/online?department=AUDIT`, {
        withCredentials: true,
        headers: sessionCookie ? { 'Cookie': sessionCookie } : {}
      });
      
      console.log('✅ Audit online users API working');
      console.log('📊 Online users in AUDIT:', auditUsersResponse.data.length);
      
      if (auditUsersResponse.data.length > 0) {
        console.log('👥 Online audit users:');
        auditUsersResponse.data.forEach(user => {
          const lastName = user.name.split(' ').pop();
          console.log(`   - ${user.name} (Last name: ${lastName})`);
        });
      }
    } catch (apiError) {
      console.log('❌ Audit online users API failed:', apiError.response?.status || apiError.message);
    }

    // Step 5: Frontend integration test
    console.log('\n5. FRONTEND INTEGRATION TEST');
    console.log('-'.repeat(40));
    
    const frontendResponse = await axios.get(`${baseURL}/`);
    console.log('✅ Frontend accessible:', frontendResponse.status === 200);
    
    // Check if online presence components are in the build
    const frontendContent = frontendResponse.data;
    const hasOnlinePresence = {
      onlineUsers: frontendContent.includes('OnlinePresenceDisplay') || frontendContent.includes('online'),
      lastNames: frontendContent.includes('lastName') || frontendContent.includes('extractLastName'),
      userBadges: frontendContent.includes('Badge') && frontendContent.includes('user'),
      tooltips: frontendContent.includes('Tooltip') && frontendContent.includes('online')
    };

    console.log('🔧 Online Presence Features:');
    console.log(`   ${hasOnlinePresence.onlineUsers ? '✅' : '❌'} Online presence display`);
    console.log(`   ${hasOnlinePresence.lastNames ? '✅' : '❌'} Last name extraction`);
    console.log(`   ${hasOnlinePresence.userBadges ? '✅' : '❌'} User badges`);
    console.log(`   ${hasOnlinePresence.tooltips ? '✅' : '❌'} Tooltip integration`);

    // Step 6: Manual testing instructions
    console.log('\n6. MANUAL TESTING INSTRUCTIONS');
    console.log('='.repeat(40));

    console.log('🎯 TO TEST ONLINE PRESENCE SYSTEM:');
    console.log('');
    console.log('📱 BROWSER TESTING:');
    console.log('1. 🌐 Open: http://localhost:8080');
    console.log('2. 🔐 Login as Finance user (FELIX AYISI)');
    console.log('3. 👀 Look at the header area (left side after department name)');
    console.log('4. 📊 You should see online presence display with:');
    console.log('   - 👥 Users icon with green color');
    console.log('   - 🏷️ Badge showing "AYISI" (your last name)');
    console.log('   - 🎨 Green background for your own badge');
    console.log('   - 💬 Tooltip showing full name and login time');

    console.log('\n🔄 MULTI-USER TESTING:');
    console.log('1. 🌐 Open another browser/incognito window');
    console.log('2. 🔐 Login as different Finance user');
    console.log('3. 👀 Both windows should show:');
    console.log('   - 👥 Multiple user badges (AYISI, [OTHER_LAST_NAME])');
    console.log('   - 🎨 Your badge in green, others in blue');
    console.log('   - 📊 Up to 6 users displayed');
    console.log('   - ➕ "+X" badge if more than 6 users online');

    console.log('\n🏢 AUDIT DEPARTMENT TESTING:');
    console.log('1. 🔐 Login as Audit user');
    console.log('2. 👀 Should see online presence for Audit department');
    console.log('3. 🔄 Real-time updates when users login/logout');

    // Step 7: Expected behavior summary
    console.log('\n7. EXPECTED BEHAVIOR SUMMARY');
    console.log('='.repeat(40));

    console.log('✅ ONLINE PRESENCE FEATURES:');
    console.log('   🏷️ Shows last names only (e.g., ASIEDU, AYISI)');
    console.log('   🎨 Your badge: Green background');
    console.log('   🎨 Other users: Blue background');
    console.log('   📊 Maximum 6 users displayed');
    console.log('   ➕ Overflow indicator for >6 users');
    console.log('   💬 Tooltips with full name and login time');
    console.log('   🔄 Real-time updates via WebSocket');
    console.log('   🏢 Department-specific display');

    console.log('\n✅ REPLACED FUNCTIONALITY:');
    console.log('   ❌ Removed: "Become Editor" button');
    console.log('   ❌ Removed: Editor/Viewer status badges');
    console.log('   ❌ Removed: Confusing editor management');
    console.log('   ✅ Added: Clean online presence display');
    console.log('   ✅ Added: Real-time user awareness');
    console.log('   ✅ Added: Department collaboration visibility');

    console.log('\n✅ USER BENEFITS:');
    console.log('   👥 See who else is working in their department');
    console.log('   🔄 Real-time awareness of team activity');
    console.log('   🎯 No confusing buttons or complex permissions');
    console.log('   📱 Clean, professional interface');
    console.log('   🏢 Works for both Finance and Audit departments');

    console.log('\n✅ ONLINE PRESENCE SYSTEM TEST COMPLETE!');
    console.log('🎯 System successfully replaced editor buttons with online presence');
    console.log('🎯 Users can now see who else is working in their department');
    console.log('🎯 Clean, intuitive interface showing last names of online users');

  } catch (error) {
    console.error('\n❌ TEST FAILED:', error.message);
    if (error.response?.data) {
      console.error('   Error details:', error.response.data);
    }
  }
}

// Run the test
testOnlinePresenceSystem().catch(console.error);
