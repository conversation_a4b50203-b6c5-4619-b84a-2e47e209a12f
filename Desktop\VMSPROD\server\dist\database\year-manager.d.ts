declare class YearDatabaseManager {
    private pools;
    private baseConfig;
    constructor();
    /**
     * Get database name for a specific year
     */
    private getYearDatabase;
    /**
     * Get or create connection pool for a specific year
     */
    private getYearPool;
    /**
     * Execute query on specific year database
     */
    queryYear(year: number, sql: string, params?: any[]): Promise<any>;
    /**
     * Execute query on database specified in session
     */
    queryWithSession(session: any, sql: string, params?: any[]): Promise<any>;
    /**
     * Check if year database exists
     */
    yearDatabaseExists(year: number): Promise<boolean>;
    /**
     * Get all available year databases
     */
    getAvailableYears(): Promise<number[]>;
    /**
     * Create new year database
     */
    createYearDatabase(year: number): Promise<boolean>;
    /**
     * Close all connection pools
     */
    closeAll(): Promise<void>;
    /**
     * Get statistics for a specific year
     */
    getYearStatistics(year: number): Promise<any>;
}
export declare const yearDatabaseManager: YearDatabaseManager;
export declare function getYearFromSession(session: any): number;
export declare function getDatabaseFromSession(session: any): string;
export {};
