const mysql = require('mysql2/promise');

async function resetSystemTime() {
  console.log('🔧 RESETTING SYSTEM TIME OVERRIDE');
  console.log('='.repeat(50));

  let connection;
  
  try {
    // Connect to database
    connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });

    console.log('✅ Connected to database');

    // Check current settings
    const [currentSettings] = await connection.execute('SELECT * FROM system_settings LIMIT 1');
    
    if (currentSettings.length === 0) {
      console.log('❌ No system settings found');
      return;
    }

    const settings = currentSettings[0];
    console.log('\n📊 CURRENT SETTINGS:');
    console.log(`   Fiscal Year Start: ${settings.fiscal_year_start}`);
    console.log(`   Fiscal Year End: ${settings.fiscal_year_end}`);
    console.log(`   Current Fiscal Year: ${settings.current_fiscal_year}`);
    console.log(`   System Time Override: ${settings.system_time}`);
    console.log(`   Auto Backup: ${settings.auto_backup_enabled}`);

    // Reset system time to current time
    const currentTime = new Date().toISOString();
    const currentYear = new Date().getFullYear();

    await connection.execute(
      'UPDATE system_settings SET system_time = ?, current_fiscal_year = ? WHERE id = ?',
      [currentTime, currentYear, settings.id]
    );

    console.log('\n✅ SYSTEM TIME RESET:');
    console.log(`   New System Time: ${currentTime}`);
    console.log(`   New Fiscal Year: ${currentYear}`);
    console.log('   System time override removed');

    // Verify the update
    const [updatedSettings] = await connection.execute('SELECT * FROM system_settings LIMIT 1');
    const updated = updatedSettings[0];

    console.log('\n📊 UPDATED SETTINGS:');
    console.log(`   Fiscal Year Start: ${updated.fiscal_year_start}`);
    console.log(`   Fiscal Year End: ${updated.fiscal_year_end}`);
    console.log(`   Current Fiscal Year: ${updated.current_fiscal_year}`);
    console.log(`   System Time: ${updated.system_time}`);
    console.log(`   Auto Backup: ${updated.auto_backup_enabled}`);

    console.log('\n🎯 RESULT:');
    console.log('✅ System time override removed');
    console.log('✅ Fiscal year set to current year (2025)');
    console.log('✅ No unwanted rollover will occur');
    console.log('✅ System is safe to restart');

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Run the reset
resetSystemTime().catch(console.error);
