import { Server, Socket } from 'socket.io';
import { logger } from '../utils/logger.js';
import { query } from '../database/db.js';

// Connected clients
const connectedClients: Map<string, {
  userId: string,
  userName: string,
  department: string,
  socket: Socket,
  lastActivity: number,
  connectionTime?: number,
  viewingResources: Set<string> // Track which resources the user is viewing
}> = new Map();

// Active resource locks
const activeLocks: Map<string, {
  userId: string,
  userName: string,
  department: string,
  expiresAt: number,
  lastActivity: number, // Track last activity time
  resourceType: string,
  resourceId: string,
  targetDepartment?: string
}> = new Map();

// Maximum concurrent users per department
const MAX_CONCURRENT_USERS: Record<string, number> = {
  'FINANCE': 10,
  'AUDIT': 10,
  'MINISTRIES': 10,
  'PENSIONS': 10,
  'PENTMEDIA': 10,
  'MISSIONS': 10,
  'PENTSOS': 10,
  'ADMINISTRATOR': 5,
  'SYSTEM ADMIN': 5
};

// Lock expiration time in milliseconds (5 minutes of inactivity)
const LOCK_EXPIRATION = 5 * 60 * 1000;

// Activity timeout in milliseconds (10 minutes)
const ACTIVITY_TIMEOUT = 10 * 60 * 1000;

// Setup socket handlers
export function setupSocketHandlers(io: Server) {
  // LAN MODE: Extract user from session cookies for WebSocket authentication
  io.use(async (socket, next) => {
    try {
      logger.info(`WebSocket connection from ${socket.handshake.address} - LAN mode, extracting user from session`);

      // Extract session from cookies
      let authenticatedUser = null;

      try {
        const cookies = socket.handshake.headers.cookie;
        if (cookies) {
          // Parse session ID from cookies
          const sessionMatch = cookies.match(/session_id=([^;]+)/);
          if (sessionMatch) {
            const sessionId = sessionMatch[1];

            // Look up user session in database
            const sessions = await query(
              'SELECT user_id, user_name, department FROM active_sessions WHERE id = ? AND is_active = TRUE',
              [sessionId]
            ) as any[];

            if (sessions.length > 0) {
              const session = sessions[0];
              authenticatedUser = {
                id: session.user_id,
                name: session.user_name,
                department: session.department,
                role: session.role || 'user',
                isAuthenticated: true
              };

              logger.info(`🔐 WebSocket authenticated user from session: ${session.user_name} (${session.department})`);
            }
          }
        }
      } catch (sessionError) {
        logger.warn('Failed to extract user from session:', sessionError instanceof Error ? sessionError.message : String(sessionError));
      }

      // Set user data (fallback to LAN user if no session found)
      socket.data.user = authenticatedUser || {
        id: 'lan-user',
        name: 'LAN User',
        department: 'GUEST',
        role: 'user',
        isAuthenticated: true // LAN users are considered authenticated
      };

      next();
    } catch (error) {
      logger.error('Socket setup error:', error);
      // Still allow connection with fallback user
      socket.data.user = {
        id: 'lan-user',
        name: 'LAN User',
        department: 'GUEST',
        role: 'user',
        isAuthenticated: true
      };
      next();
    }
  });

  // Connection handler
  io.on('connection', (socket) => {
    const userId = socket.data.user.id;
    const userName = socket.data.user.name;
    const department = socket.data.user.department;
    const isAuthenticated = socket.data.user.isAuthenticated;

    logger.info(`🔌 WebSocket connection established: ${userName} (${userId}) from ${department} [Auth: ${isAuthenticated ? 'YES' : 'NO'}]`);

    // If not authenticated, handle differently
    if (!isAuthenticated) {
      logger.warn(`⚠️ Unauthenticated WebSocket connection from ${socket.handshake.address}`);

      // Send authentication required message
      socket.emit('authentication_required', {
        message: 'Please provide a valid authentication token',
        timestamp: Date.now()
      });

      // LAN MODE: No post-connection authentication needed
      socket.on('authenticate', async (data) => {
        try {
          logger.info('Authentication request received - LAN mode, automatically successful');

          // In LAN mode, all connections are automatically authenticated
          socket.emit('authentication_success', {
            user: socket.data.user,
            message: 'LAN mode - authentication not required'
          });

          // Continue with normal connection flow using default LAN user
          setupAuthenticatedConnection(socket, socket.data.user.id, socket.data.user.name, socket.data.user.department);

        } catch (error) {
          logger.error('Post-connection authentication error:', error);
          socket.emit('authentication_failed', { error: 'Authentication failed' });
        }
      });

      // Don't continue with normal flow for unauthenticated users
      return;
    }

    // Continue with authenticated connection flow
    setupAuthenticatedConnection(socket, userId, userName, department);
  });
}

// Separate function for authenticated connection setup
async function setupAuthenticatedConnection(socket: any, userId: string, userName: string, department: string) {
  logger.info(`🚀 Setting up authenticated connection for ${userName} (${userId}) from ${department}`);

  // Check if this user is already connected from another device/browser
  const existingConnections = Array.from(connectedClients.values())
    .filter(client => client.userId === userId && client.socket.id !== socket.id);

  if (existingConnections.length > 0) {
    logger.info(`User ${userName} (${userId}) already connected from ${existingConnections.length} other sessions`);

    // We'll allow multiple connections, but log them for monitoring
    existingConnections.forEach(conn => {
      logger.info(`Existing connection: ${conn.socket.id} with last activity at ${new Date(conn.lastActivity).toISOString()}`);
    });
  }

  // Add to connected clients with current timestamp
  connectedClients.set(socket.id, {
    userId,
    userName,
    department,
    socket,
    lastActivity: Date.now(),
    connectionTime: Date.now(),
    viewingResources: new Set() // Initialize empty set of viewed resources
  });

  // Join department-specific room
  socket.join(`department:${department}`);
  logger.info(`User ${userName} (${userId}) joined room: department:${department}`);

  // If user is AUDIT or SYSTEM ADMIN, also join those rooms
  if (department === 'AUDIT' || department === 'SYSTEM ADMIN') {
    socket.join('admin-users');
    logger.info(`User ${userName} (${userId}) joined room: admin-users`);
  }

  // Join user-specific room for targeted messages
  socket.join(`user:${userId}`);
  logger.info(`User ${userName} (${userId}) joined personal room: user:${userId}`);

  // Send current locks to the client
  sendLocksToClient(socket);

  // Broadcast updated connected users list for this department
  broadcastConnectedUsers(department);

  // FIXED: Update existing HTTP session with socket info instead of creating duplicate
  try {
    // Find the user's active HTTP session and update it with socket info
    await query(
      `UPDATE active_sessions
       SET socket_id = ?, last_activity = NOW()
       WHERE user_id = ? AND is_active = TRUE
       ORDER BY session_start DESC
       LIMIT 1`,
      [socket.id, userId]
    );
    logger.info(`Updated existing session with socket info for ${userName} (${userId})`);
  } catch (error) {
    logger.error('Error updating session with socket info:', error);
  }

  // Log the number of connected clients
  const totalClients = connectedClients.size;
  const departmentClients = Array.from(connectedClients.values())
    .filter(client => client.department === department).length;

  logger.info(`Total connected clients: ${totalClients}, ${departmentClients} in department ${department}`);
  console.log(`Connected clients: ${Array.from(connectedClients.values()).map(c => `${c.userName} (${c.userId})`).join(', ')}`);

  // Update activity timestamp on any event
  socket.onAny(() => {
    const client = connectedClients.get(socket.id);
    if (client) {
      client.lastActivity = Date.now();
      connectedClients.set(socket.id, client);
    }
  });

  // Force refresh vouchers for this user's department
  socket.emit('force_refresh', {
    type: 'vouchers',
    department: department,
    timestamp: Date.now()
  });

  // Log all rooms this socket is in
  const rooms = Array.from(socket.rooms.values());
  logger.info(`Socket ${socket.id} is in rooms: ${rooms.join(', ')}`);

  // Notify other users in the same department that a new user has connected
  socket.to(`department:${department}`).emit('user_joined', {
    userId,
    userName,
    department,
    timestamp: Date.now()
  });

    // Handle get_department_users request
    socket.on('get_department_users', (data: any, callback: any) => {
      try {
        const { department: requestedDept } = data;
        const now = Date.now();

        // FIXED: Get unique users only (no duplicates)
        const uniqueUsers = new Map<string, any>();

        Array.from(connectedClients.values())
          .filter(client => client.department === requestedDept)
          .forEach(client => {
            const isActive = now - client.lastActivity < ACTIVITY_TIMEOUT;

            // Only keep the most recent connection for each user
            const existingUser = uniqueUsers.get(client.userId);
            if (!existingUser || client.lastActivity > existingUser.lastActivity) {
              uniqueUsers.set(client.userId, {
                id: client.userId,
                name: client.userName,
                department: client.department,
                isActive: isActive,
                lastActivity: client.lastActivity,
                // Check if user has any active locks
                isEditing: Array.from(activeLocks.values()).some(lock => lock.userId === client.userId)
              });
            }
          });

        const departmentUsers = Array.from(uniqueUsers.values());

        logger.info(`Returning ${departmentUsers.length} users for department ${requestedDept}`);
        console.log('Department users:', departmentUsers.map(u => `${u.name} (${u.isActive ? 'active' : 'inactive'})`));

        callback({
          success: true,
          users: departmentUsers,
          department: requestedDept,
          timestamp: now
        });
      } catch (error) {
        logger.error('Error processing get_department_users request:', error);
        callback({ success: false, message: 'Server error' });
      }
    });

    // Handle get_locks request
    socket.on('get_locks', (callback: any) => {
      try {
        // Clean up expired locks first
        cleanupExpiredLocks();

        // Get all locks
        const locks = Array.from(activeLocks.entries()).map(([key, lock]) => ({
          key,
          userId: lock.userId,
          userName: lock.userName,
          department: lock.department,
          expiresAt: lock.expiresAt,
          resourceType: lock.resourceType,
          resourceId: lock.resourceId,
          // Calculate remaining time in seconds
          remainingTime: Math.max(0, Math.floor((lock.expiresAt - Date.now()) / 1000))
        }));

        logger.info(`Returning ${locks.length} active locks`);
        if (locks.length > 0) {
          console.log('Active locks:', locks.map(l => `${l.key} by ${l.userName} (expires in ${l.remainingTime}s)`));
        }

        callback({
          success: true,
          locks: locks,
          timestamp: Date.now()
        });
      } catch (error) {
        logger.error('Error processing get_locks request:', error);
        callback({ success: false, message: 'Server error' });
      }
    });

    // Handle disconnect
    socket.on('disconnect', async () => {
      logger.info(`User disconnected: ${userName} (${userId}) from department ${department}`);

      // Get the client before removing it
      const client = connectedClients.get(socket.id);

      // Broadcast resource viewer updates for all resources this user was viewing
      if (client && client.viewingResources.size > 0) {
        // Get all resources this user was viewing
        const viewedResources = Array.from(client.viewingResources);

        logger.info(`User ${userName} (${userId}) was viewing ${viewedResources.length} resources`);

        // Remove from connected clients
        connectedClients.delete(socket.id);

        // Broadcast updated viewer counts for each resource
        viewedResources.forEach(resourceKey => {
          broadcastResourceViewers(resourceKey);
        });
      } else {
        // Remove from connected clients
        connectedClients.delete(socket.id);
      }

      // Check if this was the user's last connection
      const remainingConnections = Array.from(connectedClients.values())
        .filter(client => client.userId === userId);

      if (remainingConnections.length === 0) {
        logger.info(`User ${userName} (${userId}) has no remaining connections`);

        // Release any locks held by this user
        releaseUserLocks(userId);
      } else {
        logger.info(`User ${userName} (${userId}) still has ${remainingConnections.length} active connections`);
      }

      // FIXED: Clear socket info from session but keep session active (user might still be logged in via HTTP)
      try {
        await query(
          `UPDATE active_sessions
           SET socket_id = NULL, last_activity = NOW()
           WHERE user_id = ? AND socket_id = ?`,
          [userId, socket.id]
        );
        logger.info(`Cleared socket info for ${userName} (${userId}) - session remains active`);
      } catch (error) {
        logger.error('Error clearing socket info from session:', error);
      }

      // Broadcast updated connected users list for this department
      broadcastConnectedUsers(department);

      // Notify other users in the same department that a user has disconnected
      socket.to(`department:${department}`).emit('user_left', {
        userId,
        userName,
        department,
        timestamp: Date.now()
      });

      // Log the number of connected clients
      const totalClients = connectedClients.size;
      const departmentClients = Array.from(connectedClients.values())
        .filter(client => client.department === department).length;

      logger.info(`After disconnect - Total connected clients: ${totalClients}, ${departmentClients} in department ${department}`);
    });

    // Handle heartbeat
    socket.on('heartbeat', () => {
      // Respond with a heartbeat acknowledgment
      socket.emit('heartbeat_ack', { timestamp: Date.now() });
    });

    // Handle resource view tracking
    socket.on('view_resource', (data: any, callback: any) => {
      const { resourceType, resourceId, targetDepartment, isViewing } = data;

      try {
        const client = connectedClients.get(socket.id);
        if (!client) {
          if (callback) callback({ success: false, message: 'Client not found' });
          return;
        }

        // For Audit users, we can specify which department's resources they want to view
        const effectiveResourceId = department === 'AUDIT' && targetDepartment ?
          `${targetDepartment}-${resourceId}` : resourceId;

        const resourceKey = `${resourceType}:${effectiveResourceId}`;

        // Update the client's viewing resources
        if (isViewing) {
          client.viewingResources.add(resourceKey);
          logger.info(`User ${userName} (${userId}) is now viewing ${resourceKey}`);
        } else {
          client.viewingResources.delete(resourceKey);
          logger.info(`User ${userName} (${userId}) is no longer viewing ${resourceKey}`);
        }

        // Update the client record
        client.lastActivity = Date.now();
        connectedClients.set(socket.id, client);

        // Broadcast the updated viewer count for this resource
        broadcastResourceViewers(resourceKey);

        // If this is an Audit user viewing a specific department's resources,
        // also broadcast to that department
        if (department === 'AUDIT' && targetDepartment) {
          broadcastConnectedUsers(targetDepartment);
        }

        // Automatically acquire lock if viewing and not already locked
        if (isViewing && !activeLocks.has(resourceKey)) {
          // Check if another user from the same department already has a lock
          let departmentHasLock = false;
          for (const lock of activeLocks.values()) {
            if (lock.department === department && lock.userId !== userId) {
              departmentHasLock = true;
              break;
            }
          }

          // If no other user has a lock, automatically acquire one
          if (!departmentHasLock) {
            const now = Date.now();
            const newLock = {
              userId,
              userName,
              department,
              expiresAt: now + LOCK_EXPIRATION,
              lastActivity: now,
              resourceType,
              resourceId: effectiveResourceId,
              targetDepartment: department === 'AUDIT' ? targetDepartment : undefined
            };

            // Add the lock
            activeLocks.set(resourceKey, newLock);

            // Log the automatic lock acquisition
            logger.info(`Lock automatically acquired for ${resourceKey} by ${userName} (${userId})`);

            // Broadcast lock update to all clients
            broadcastLockUpdate(resourceKey, true, userId);
          }
        }

        if (callback) {
          callback({
            success: true,
            message: isViewing ? 'Now viewing resource' : 'No longer viewing resource',
            viewerCount: getResourceViewerCount(resourceKey)
          });
        }
      } catch (error) {
        logger.error('Error processing view_resource request:', error);
        if (callback) callback({ success: false, message: 'Server error' });
      }
    });

    // Handle activity update - used to extend lock expiration
    socket.on('activity', (data: any, callback: any) => {
      const { resourceType, resourceId, targetDepartment } = data;

      // For Audit users, we need to use the same key format as when acquiring the lock
      const effectiveResourceId = department === 'AUDIT' && targetDepartment ?
        `${targetDepartment}-${resourceId}` : resourceId;

      const lockKey = `${resourceType}:${effectiveResourceId}`;

      try {
        // Check if the lock exists and is owned by this user
        if (activeLocks.has(lockKey) && activeLocks.get(lockKey)!.userId === userId) {
          const lock = activeLocks.get(lockKey)!;
          const now = Date.now();

          // Update last activity time and extend expiration
          lock.lastActivity = now;
          lock.expiresAt = now + LOCK_EXPIRATION;
          activeLocks.set(lockKey, lock);

          logger.info(`Activity update for lock ${lockKey} by ${userName} (${userId})`);

          if (callback) {
            callback({
              success: true,
              message: 'Activity recorded',
              expiresAt: lock.expiresAt
            });
          }
        } else if (callback) {
          callback({
            success: false,
            message: 'Lock not found or not owned by you'
          });
        }
      } catch (error) {
        logger.error('Error processing activity update:', error);
        if (callback) {
          callback({ success: false, message: 'Server error' });
        }
      }
    });

    // Handle lock request
    socket.on('lock_request', (data: any, callback: any) => {
      const { resourceType, resourceId, targetDepartment } = data;

      // For Audit users, we can specify which department's vouchers they want to edit
      // This allows multiple Audit users to edit vouchers from different departments simultaneously
      const effectiveResourceId = department === 'AUDIT' && targetDepartment ?
        `${targetDepartment}-${resourceId}` : resourceId;

      let lockKey = `${resourceType}:${effectiveResourceId}`;

      // Log the lock request details
      logger.info(`Lock request from ${userName} (${userId}) in ${department} for ${lockKey}${targetDepartment ? ' (targeting '+targetDepartment+')' : ''}`);

      try {
        // SMART BATCH LOCKING: Special handling for batch operations
        if (resourceType === 'batch-operation') {
          // For batch operations, use department-wide locking regardless of specific resource ID
          const departmentBatchLockKey = `batch-operation:${department}`;

          // Check if department already has an active batch operation
          if (activeLocks.has(departmentBatchLockKey)) {
            const existingLock = activeLocks.get(departmentBatchLockKey)!;

            // If lock has expired, remove it
            if (existingLock.expiresAt < Date.now()) {
              activeLocks.delete(departmentBatchLockKey);
              logger.info(`Expired batch operation lock removed for ${department}`);
            } else if (existingLock.userId !== userId) {
              // Another user has the batch operation lock
              logger.info(`Batch operation lock denied for ${lockKey} - ${existingLock.userName} is performing batch operation`);
              callback({
                success: false,
                message: `${existingLock.userName} is currently performing a batch operation. Please wait.`
              });
              return;
            } else {
              // Same user extending the lock
              const now = Date.now();
              existingLock.lastActivity = now;
              existingLock.expiresAt = now + LOCK_EXPIRATION;
              activeLocks.set(departmentBatchLockKey, existingLock);
              logger.info(`Batch operation lock extended for ${userName} in ${department}`);
              callback({ success: true, message: 'Batch operation lock extended' });
              return;
            }
          }

          // Override the lockKey for batch operations to be department-wide
          lockKey = departmentBatchLockKey;
        }

        // Check if the resource is already locked
        if (activeLocks.has(lockKey)) {
          const lock = activeLocks.get(lockKey)!;

          // If lock has expired, release it
          if (lock.expiresAt < Date.now()) {
            activeLocks.delete(lockKey);
            logger.info(`Expired lock for ${lockKey} removed`);
          }
          // If lock is held by the same user, extend it
          else if (lock.userId === userId) {
            const now = Date.now();
            lock.lastActivity = now;
            lock.expiresAt = now + LOCK_EXPIRATION;
            activeLocks.set(lockKey, lock);
            logger.info(`Lock extended for ${lockKey} by ${userName} (${userId})`);
            callback({ success: true, message: 'Lock extended' });
            return;
          }
          // If lock is held by another user, deny the request
          else {
            logger.info(`Lock request denied for ${lockKey} - already locked by ${lock.userName} (${lock.userId})`);
            callback({
              success: false,
              message: `Resource is locked by ${lock.userName || 'another user'}`,
              lockOwner: lock.userName
            });
            return;
          }
        }

        // For Audit users, we check locks differently - they can have one lock per department
        if (department === 'AUDIT') {
          // Count existing locks for this Audit user targeting the specific department
          let userDepartmentLockCount = 0;
          const targetDeptPrefix = targetDepartment ? `${targetDepartment}-` : '';

          for (const [key, lock] of activeLocks.entries()) {
            // If this is an Audit user's lock for the target department
            if (lock.userId === userId && key.includes(targetDeptPrefix)) {
              userDepartmentLockCount++;
            }
          }

          // Check if another Audit user already has a lock for this department
          let otherAuditUserHasLock = false;
          for (const [key, lock] of activeLocks.entries()) {
            if (lock.userId !== userId && lock.department === 'AUDIT' && key.includes(targetDeptPrefix)) {
              otherAuditUserHasLock = true;
              logger.info(`Another Audit user (${lock.userName}) already has a lock for ${targetDepartment}`);
              break;
            }
          }

          if (otherAuditUserHasLock) {
            callback({
              success: false,
              message: `Another Audit user is already editing ${targetDepartment} vouchers`
            });
            return;
          }
        } else {
          // For non-Audit users, use the original department-wide lock count
          let departmentLockCount = 0;
          for (const lock of activeLocks.values()) {
            if (lock.department === department) {
              departmentLockCount++;
            }
          }

          // Check if department has reached its concurrent user limit
          if (departmentLockCount >= (MAX_CONCURRENT_USERS[department] || 4)) {
            logger.info(`Department ${department} has reached maximum concurrent users`);
            callback({ success: false, message: 'Department has reached maximum concurrent users' });
            return;
          }
        }

        // Create a new lock
        const now = Date.now();
        const newLock = {
          userId,
          userName,
          department,
          expiresAt: now + LOCK_EXPIRATION,
          lastActivity: now, // Initialize last activity time
          resourceType,
          resourceId: effectiveResourceId,
          targetDepartment: department === 'AUDIT' ? targetDepartment : undefined
        };

        // Add the lock
        activeLocks.set(lockKey, newLock);

        // Log the lock acquisition
        logger.info(`Lock acquired for ${lockKey} by ${userName} (${userId}) in department ${department}${targetDepartment ? ' for '+targetDepartment+' vouchers' : ''}`);

        // Broadcast lock update to all clients
        broadcastLockUpdate(lockKey, true, userId);

        // Also broadcast updated connected users for this department
        broadcastConnectedUsers(department);

        // If this is an Audit user locking a specific department's vouchers,
        // also broadcast to that department
        if (department === 'AUDIT' && targetDepartment) {
          broadcastConnectedUsers(targetDepartment);
        }

        callback({
          success: true,
          message: department === 'AUDIT' && targetDepartment ?
            `You are now editing ${targetDepartment} vouchers` :
            'Lock acquired'
        });
      } catch (error) {
        logger.error('Error processing lock request:', error);
        callback({ success: false, message: 'Server error' });
      }
    });

    // Handle lock release
    socket.on('lock_release', (data: any, callback: any) => {
      const { resourceType, resourceId, targetDepartment } = data;

      // For Audit users, we need to use the same key format as when acquiring the lock
      const effectiveResourceId = department === 'AUDIT' && targetDepartment ?
        `${targetDepartment}-${resourceId}` : resourceId;

      const lockKey = `${resourceType}:${effectiveResourceId}`;

      try {
        // Check if the lock exists and is owned by this user
        if (activeLocks.has(lockKey) && activeLocks.get(lockKey)!.userId === userId) {
          const lock = activeLocks.get(lockKey)!;
          const targetDept = lock.targetDepartment;

          // Log the lock release
          logger.info(`Lock released for ${lockKey} by ${lock.userName || 'Unknown'} (${userId}) in department ${lock.department}${targetDept ? ' for '+targetDept+' vouchers' : ''}`);

          // Release the lock
          activeLocks.delete(lockKey);

          // Broadcast lock update to all clients
          broadcastLockUpdate(lockKey, false);

          // Also broadcast updated connected users for this department
          broadcastConnectedUsers(department);

          // If this was an Audit user's lock for a specific department, also update that department
          if (department === 'AUDIT' && targetDept) {
            broadcastConnectedUsers(targetDept);
          }

          callback({
            success: true,
            message: department === 'AUDIT' && targetDept ?
              `You are no longer editing ${targetDept} vouchers` :
              'Lock released'
          });
        } else {
          callback({ success: false, message: 'Lock not found or not owned by you' });
        }
      } catch (error) {
        logger.error('Error processing lock release:', error);
        callback({ success: false, message: 'Server error' });
      }
    });

    // Handle state update
    socket.on('state_update', (data: any) => {
      // Broadcast state update to all clients except sender
      socket.broadcast.emit('state_update', data);
    });

    // CRITICAL FIX: Handle explicit department join
    socket.on('join_department', (data: any) => {
      const { department, userId, userName } = data;

      logger.info(`User ${userName} (${userId}) explicitly joining department:${department} room`);
      console.log(`User ${userName} (${userId}) explicitly joining department:${department} room`);

      // Join department-specific room
      socket.join(`department:${department}`);

      // Update client record
      const client = connectedClients.get(socket.id);
      if (client) {
        client.lastActivity = Date.now();
        connectedClients.set(socket.id, client);
      } else {
        // If client record doesn't exist, create it
        logger.info(`Creating new client record for ${userName} (${userId}) in department ${department}`);
        connectedClients.set(socket.id, {
          userId,
          userName,
          department,
          socket,
          lastActivity: Date.now(),
          connectionTime: Date.now(),
          viewingResources: new Set() // Initialize empty set of viewed resources
        });
      }

      // Log all connected clients for debugging
      logger.info(`Connected clients after join: ${Array.from(connectedClients.values()).map(c => `${c.userName} (${c.department})`).join(', ')}`);
      console.log(`Connected clients after join: ${Array.from(connectedClients.values()).map(c => `${c.userName} (${c.department})`).join(', ')}`);

      // Broadcast updated connected users list for this department
      broadcastConnectedUsers(department);
    });

    // REMOVED: Duplicate presence announcement handler that was causing triple notifications

  // Periodically clean up expired locks and inactive locks
  setInterval(() => {
    cleanupExpiredLocks();
  }, 30000); // Check every 30 seconds
}

// Send current locks to a client
function sendLocksToClient(socket: Socket) {
  // Clean up expired locks first
  cleanupExpiredLocks();

  const now = Date.now();

  const locks = Array.from(activeLocks.entries()).map(([key, lock]) => {
    const [resourceType, resourceId] = key.split(':');

    return {
      key,
      userId: lock.userId,
      userName: lock.userName || 'Unknown',
      department: lock.department,
      expiresAt: lock.expiresAt,
      resourceType: lock.resourceType || resourceType,
      resourceId: lock.resourceId || resourceId,
      // Calculate remaining time in seconds
      remainingTime: Math.max(0, Math.floor((lock.expiresAt - now) / 1000))
    };
  });

  socket.emit('locks_update', {
    locks,
    timestamp: now
  });

  logger.info(`Sent ${locks.length} active locks to client`);
}

// Store the io instance
let ioInstance: Server | null = null;

// Set the io instance
export function setIoInstance(io: Server) {
  ioInstance = io;
}

// Broadcast lock update to all clients
function broadcastLockUpdate(lockKey: string, isLocked: boolean, userId?: string) {
  if (!ioInstance) return;

  // Get more information about the lock
  let lockInfo: any = { key: lockKey, isLocked };

  if (isLocked && userId) {
    // Get user information
    const client = Array.from(connectedClients.values()).find(c => c.userId === userId);
    const userName = client ? client.userName : 'Unknown';
    const department = client ? client.department : 'Unknown';

    // Get resource information
    const [resourceType, resourceId] = lockKey.split(':');

    // Check if this is a department-specific lock for Audit
    let targetDepartment = undefined;
    if (department === 'AUDIT' && resourceId.includes('-')) {
      const parts = resourceId.split('-');
      if (parts.length > 1) {
        targetDepartment = parts[0];
      }
    }

    lockInfo = {
      ...lockInfo,
      userId,
      userName,
      department,
      resourceType,
      resourceId,
      targetDepartment,
      timestamp: Date.now(),
      expiresAt: Date.now() + LOCK_EXPIRATION
    };

    // If this is an Audit user with a department-specific lock, also emit to that department
    if (department === 'AUDIT' && targetDepartment) {
      ioInstance.to(`department:${targetDepartment}`).emit('lock_update', {
        ...lockInfo,
        forDepartment: targetDepartment
      });

      logger.info(`Broadcasting department-specific lock update to ${targetDepartment}: ${lockKey} is now ${isLocked ? 'locked' : 'unlocked'} by ${userName}`);
    }
  }

  // Emit the lock update to all clients
  ioInstance.emit('lock_update', lockInfo);

  // Log the broadcast
  logger.info(`Broadcasting lock update: ${lockKey} is now ${isLocked ? 'locked' : 'unlocked'}${isLocked ? ` by ${lockInfo.userName} (${userId})` : ''}`);
}

// Broadcast all lock updates to all clients
function broadcastLockUpdates() {
  if (!ioInstance) return;

  const now = Date.now();

  // Clean up expired locks first
  cleanupExpiredLocks();

  // Get all locks
  const locks = Array.from(activeLocks.entries()).map(([key, lock]) => {
    const [resourceType, resourceId] = key.split(':');

    return {
      key,
      userId: lock.userId,
      userName: lock.userName || 'Unknown',
      department: lock.department,
      expiresAt: lock.expiresAt,
      resourceType: lock.resourceType || resourceType,
      resourceId: lock.resourceId || resourceId,
      // Calculate remaining time in seconds
      remainingTime: Math.max(0, Math.floor((lock.expiresAt - now) / 1000))
    };
  });

  // Emit to all clients
  ioInstance.emit('locks_update', {
    locks,
    timestamp: now
  });

  logger.info(`Broadcasting ${locks.length} active locks to all clients`);
}

// Clean up expired locks and inactive locks
function cleanupExpiredLocks() {
  const now = Date.now();
  const expiredLockKeys: string[] = [];

  // Find expired locks or locks with no activity for 5 minutes
  activeLocks.forEach((lock, key) => {
    // Check if lock has expired
    if (lock.expiresAt <= now) {
      expiredLockKeys.push(key);
    }
    // Check if there's been no activity for 5 minutes
    else if (now - lock.lastActivity >= LOCK_EXPIRATION) {
      expiredLockKeys.push(key);
      logger.info(`Lock ${key} has been inactive for 5 minutes`);
    }
  });

  // Delete expired locks
  expiredLockKeys.forEach(key => {
    const lock = activeLocks.get(key);
    if (lock) {
      logger.info(`Auto-releasing lock ${key} for user ${lock.userName || 'Unknown'} (${lock.userId}) - ${lock.expiresAt <= now ? 'expired' : 'inactive for 5 minutes'}`);
      activeLocks.delete(key);
      broadcastLockUpdate(key, false);
    }
  });

  // Log if any locks were released
  if (expiredLockKeys.length > 0) {
    logger.info(`Released ${expiredLockKeys.length} locks (expired or inactive)`);
  }

  return expiredLockKeys.length;
}

// Release all locks held by a user
function releaseUserLocks(userId: string) {
  const userLockKeys: string[] = [];

  // Find all locks held by this user
  for (const [lockKey, lock] of activeLocks.entries()) {
    if (lock.userId === userId) {
      userLockKeys.push(lockKey);
    }
  }

  // Release each lock
  userLockKeys.forEach(key => {
    const lock = activeLocks.get(key);
    if (lock) {
      logger.info(`Auto-releasing lock ${key} for disconnected user ${lock.userName || 'Unknown'} (${userId})`);
      activeLocks.delete(key);
      broadcastLockUpdate(key, false);
    }
  });

  // Log if any locks were released
  if (userLockKeys.length > 0) {
    logger.info(`Released ${userLockKeys.length} locks for disconnected user ${userId}`);
  }

  return userLockKeys.length;
}

// Broadcast user update to all clients
export function broadcastUserUpdate(type: string, userData: any) {
  if (!ioInstance) return;

  logger.info(`Broadcasting user update: ${type} for user ${userData.name} (${userData.id})`);
  console.log(`Broadcasting user update: ${type}`, userData);

  // Get all connected clients
  const clientCount = ioInstance.sockets.sockets.size;
  logger.info(`Broadcasting to ${clientCount} connected clients`);

  // Make sure isActive is a boolean for consistency
  const normalizedUserData = {
    ...userData,
    isActive: Boolean(userData.isActive || userData.is_active)
  };

  // Remove any internal properties
  delete normalizedUserData._originalIsActive;

  // Log the normalized data
  console.log(`Broadcasting normalized user data:`, normalizedUserData);

  // Emit to all clients
  ioInstance.emit('user_update', {
    type,
    user: normalizedUserData,
    timestamp: Date.now()
  });

  // Also emit to specific department room if available
  if (userData.department) {
    logger.info(`Emitting to department room: ${userData.department}`);
    ioInstance.to(`department:${userData.department}`).emit('user_update', {
      type,
      user: normalizedUserData,
      timestamp: Date.now()
    });
  }

  // Also emit to AUDIT and SYSTEM ADMIN rooms
  ioInstance.to('department:AUDIT').emit('user_update', {
    type,
    user: normalizedUserData,
    timestamp: Date.now()
  });

  ioInstance.to('department:SYSTEM ADMIN').emit('user_update', {
    type,
    user: normalizedUserData,
    timestamp: Date.now()
  });

  // If this is a user status change, update the connected users list
  if (type === 'updated' && 'is_active' in userData) {
    // Find the user's department
    const department = userData.department;
    if (department) {
      // Broadcast updated connected users for this department
      broadcastConnectedUsers(department);
    }
  }

  // Log success
  logger.info(`Broadcast complete for user ${userData.name} (${userData.id})`);
}

// Broadcast registration update to all clients
export function broadcastRegistrationUpdate(type: string, registrationData: any) {
  if (!ioInstance) return;

  ioInstance.emit('registration_update', {
    type,
    registration: registrationData
  });
}

// Broadcast voucher update to all relevant clients
export function broadcastVoucherUpdate(type: string, voucherData: any) {
  if (!ioInstance) {
    logger.error('Socket.IO instance not initialized');
    return;
  }

  try {
    const { id: voucherId, department } = voucherData;
    const timestamp = Date.now();

    // Log the broadcast attempt
    logger.info(`Broadcasting voucher update for ${voucherId} to department ${department}`);

    // CRITICAL FIX: Use ONLY room-based broadcast to prevent triple notifications
    // Broadcast to department room
    ioInstance.to(`department:${department}`).emit('voucher_update', {
      type,
      voucher: voucherData,
      timestamp
    });

    // Also broadcast to audit and system admin rooms
    ioInstance.to('department:AUDIT').emit('voucher_update', {
      type,
      voucher: voucherData,
      timestamp
    });

    ioInstance.to('department:SYSTEM ADMIN').emit('voucher_update', {
      type,
      voucher: voucherData,
      timestamp
    });

    // Log success
    logger.info(`Successfully broadcasted voucher update for ${voucherId} to department rooms (single broadcast per room)`);

  } catch (error) {
    logger.error('Error broadcasting voucher update:', error);
  }
}

// Broadcast batch update to all clients
export function broadcastBatchUpdate(type: string, batchData: any) {
  if (!ioInstance) return;

  ioInstance.emit('batch_update', {
    type,
    batch: batchData
  });
}

// Broadcast notification update to all clients
export function broadcastNotificationUpdate(type: string, notificationData: any) {
  if (!ioInstance) return;

  ioInstance.emit('notification_update', {
    type,
    notification: notificationData
  });
}

// Generic data update broadcaster for any entity type
export function broadcastDataUpdate(entityType: string, actionType: string, data: any) {
  if (!ioInstance) return;

  const identifier = data.id || data.voucher_id || data._id || 'unknown';
  logger.info(`Broadcasting ${actionType} for ${entityType}: ${identifier}`);
  console.log(`Broadcasting ${actionType} for ${entityType}:`, identifier, data);

  // Get all connected clients
  const clientCount = ioInstance.sockets.sockets.size;
  logger.info(`Broadcasting to ${clientCount} connected clients`);

  // Emit to all clients
  ioInstance.emit('data_update', {
    entityType,
    actionType,
    data
  });

  // Log success
  logger.info(`Broadcast complete for ${entityType} ${identifier}`);
}

// Get the number of viewers for a resource
function getResourceViewerCount(resourceKey: string): number {
  let count = 0;
  for (const client of connectedClients.values()) {
    if (client.viewingResources.has(resourceKey)) {
      count++;
    }
  }
  return count;
}

// Get all users viewing a resource
function getResourceViewers(resourceKey: string): Array<{userId: string, userName: string, department: string}> {
  const viewers: Array<{userId: string, userName: string, department: string}> = [];
  for (const client of connectedClients.values()) {
    if (client.viewingResources.has(resourceKey)) {
      viewers.push({
        userId: client.userId,
        userName: client.userName,
        department: client.department
      });
    }
  }
  return viewers;
}

// Broadcast resource viewers to all clients
function broadcastResourceViewers(resourceKey: string) {
  if (!ioInstance) return;

  const viewers = getResourceViewers(resourceKey);
  const viewerCount = viewers.length;

  logger.info(`Broadcasting ${viewerCount} viewers for resource ${resourceKey}`);

  ioInstance.emit('resource_viewers', {
    resourceKey,
    viewers,
    viewerCount,
    timestamp: Date.now()
  });
}

// Broadcast connected users for a department
function broadcastConnectedUsers(department: string) {
  if (!ioInstance) return;

  const now = Date.now();

  // FIXED: Get unique users only (no duplicates)
  const uniqueUsers = new Map<string, any>();

  Array.from(connectedClients.values())
    .filter(client => client.department === department)
    .forEach(client => {
      const isActive = now - client.lastActivity < ACTIVITY_TIMEOUT;

      // Only keep the most recent connection for each user
      const existingUser = uniqueUsers.get(client.userId);
      if (!existingUser || client.lastActivity > existingUser.lastActivity) {
        uniqueUsers.set(client.userId, {
          id: client.userId,
          name: client.userName,
          department: client.department,
          isActive: isActive,
          lastActivity: client.lastActivity,
          // Check if user has any active locks
          isEditing: Array.from(activeLocks.values()).some(lock => lock.userId === client.userId)
        });
      }
    });

  const departmentUsers = Array.from(uniqueUsers.values());

  // Log the users
  logger.info(`Broadcasting ${departmentUsers.length} connected users for department ${department}`);
  console.log('Department users:', departmentUsers.map(u => `${u.name} (${u.isActive ? 'active' : 'inactive'})`));

  // Emit to department-specific room
  ioInstance.to(`department:${department}`).emit('connected_users', {
    users: departmentUsers,
    department: department,
    timestamp: now
  });

  // Also emit to AUDIT and SYSTEM ADMIN rooms
  if (department !== 'AUDIT') {
    ioInstance.to('department:AUDIT').emit('connected_users', {
      users: departmentUsers,
      department: department,
      timestamp: now
    });
  }

  if (department !== 'SYSTEM ADMIN') {
    ioInstance.to('department:SYSTEM ADMIN').emit('connected_users', {
      users: departmentUsers,
      department: department,
      timestamp: now
    });
  }

  // Also emit to all clients for backward compatibility
  ioInstance.emit('connected_users', {
    users: departmentUsers,
    department: department,
    timestamp: now
  });

  // Also broadcast active locks for this department
  const departmentLocks = Array.from(activeLocks.entries())
    .filter(([_, lock]) => lock.department === department)
    .map(([key, lock]) => {
      const resourceId = key.split(':')[1];
      const resourceType = key.split(':')[0];

      return {
        key,
        userId: lock.userId,
        userName: lock.userName || 'Unknown',
        department: lock.department,
        expiresAt: lock.expiresAt,
        resourceType,
        resourceId,
        // Calculate remaining time in seconds
        remainingTime: Math.max(0, Math.floor((lock.expiresAt - now) / 1000))
      };
    });

  ioInstance.emit('department_locks', {
    locks: departmentLocks,
    department: department,
    timestamp: now
  });
}
