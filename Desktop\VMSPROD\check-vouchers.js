const mysql = require('mysql2/promise');

async function checkCurrentVouchers() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });

  console.log('🔍 CURRENT VOUCHER STATUS:');
  console.log('='.repeat(50));

  // Check all active vouchers
  const [allVouchers] = await connection.execute(`
    SELECT voucher_id, status, department, original_department, reference_id, deleted, sent_to_audit, received_by_audit
    FROM vouchers 
    WHERE deleted = FALSE
    ORDER BY voucher_id, created_at
  `);

  console.log('\n📋 ALL ACTIVE VOUCHERS:');
  console.log('-'.repeat(60));
  if (allVouchers.length === 0) {
    console.log('❌ No active vouchers found');
  } else {
    allVouchers.forEach(v => {
      console.log(`📄 ${v.voucher_id}: ${v.status}`);
      console.log(`   Dept: ${v.department} | OrigDept: ${v.original_department || 'None'}`);
      console.log(`   Ref: ${v.reference_id || 'None'} | SentToAudit: ${v.sent_to_audit} | ReceivedByAudit: ${v.received_by_audit}`);
      console.log('');
    });
  }

  // Check Finance Processing tab using EXACT frontend filtering logic
  const [financeProcessing] = await connection.execute(`
    SELECT voucher_id, status, department, original_department, reference_id, sent_to_audit
    FROM vouchers
    WHERE original_department = 'FINANCE'
      AND sent_to_audit = TRUE
      AND status NOT IN ('VOUCHER CERTIFIED', 'VOUCHER REJECTED', 'VOUCHER RETURNED')
      AND deleted = FALSE
    ORDER BY voucher_id
  `);

  console.log('\n📋 FINANCE PROCESSING TAB (Frontend Logic):');
  console.log('-'.repeat(50));
  if (financeProcessing.length === 0) {
    console.log('✅ No vouchers in Finance Processing tab');
  } else {
    financeProcessing.forEach(v => {
      console.log(`📄 ${v.voucher_id}: ${v.status} | Dept: ${v.department} | Ref: ${v.reference_id || 'None'}`);
    });
  }

  // Check Finance Certified tab specifically
  const [financeCertified] = await connection.execute(`
    SELECT voucher_id, status, department, original_department, reference_id
    FROM vouchers 
    WHERE status = 'VOUCHER CERTIFIED'
      AND (department = 'FINANCE' OR original_department = 'FINANCE')
      AND deleted = FALSE
    ORDER BY voucher_id
  `);

  console.log('\n✅ FINANCE CERTIFIED TAB:');
  console.log('-'.repeat(40));
  if (financeCertified.length === 0) {
    console.log('❌ No vouchers in Finance Certified tab');
  } else {
    financeCertified.forEach(v => {
      console.log(`📄 ${v.voucher_id}: ${v.status} | Dept: ${v.department} | OrigDept: ${v.original_department || 'None'} | Ref: ${v.reference_id || 'None'}`);
    });
  }

  await connection.end();
}

checkCurrentVouchers().catch(console.error);
