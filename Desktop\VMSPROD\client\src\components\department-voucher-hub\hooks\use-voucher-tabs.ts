
import { useState, useEffect } from 'react';
import { Department, Voucher } from '@/lib/types';
import { useAppStore } from '@/lib/store';
import { VOUCHER_STATUSES } from '@/lib/constants/voucher-statuses';

export const useVoucherTabs = (department: Department) => {
  const vouchers = useAppStore((state) => state.vouchers);
  const fetchVouchers = useAppStore((state) => state.fetchVouchers);
  const [activeTab, setActiveTab] = useState('new-vouchers');

  // Fetch vouchers on department change - add fetchVouchers to dependencies to prevent stale closure
  useEffect(() => {
    console.log(`🔄 VOUCHER HUB: Fetching vouchers for department: ${department}`);
    fetchVouchers(); // Fetch all vouchers for real-time updates
  }, [department, fetchVouchers]);

  // Debug logging for voucher counts
  useEffect(() => {
    console.log(`📊 VOUCHER HUB [${department}]: Total vouchers: ${vouchers.length}`);
    console.log(`📊 VOUCHER HUB [${department}]: Dispatched vouchers: ${vouchers.filter(v =>
      v.originalDepartment === department &&
      (v.dispatched === true || v.auditDispatchedBy || v.auditDispatchTime)
    ).length}`);
  }, [vouchers, department]);

  // Removed problematic useEffect calls that were causing infinite loops

  // SIMPLE FILTERING: Clear business rules
  const newVouchers = vouchers.filter(v => {
    // For audit dashboard viewing department hubs (e.g., FINANCE VOUCHER HUB)
    return v.originalDepartment === department &&
           v.department === 'AUDIT' &&
           v.status === VOUCHER_STATUSES.AUDIT_PROCESSING &&
           v.receivedByAudit === true &&
           v.workStarted !== true &&  // No work started yet
           !v.dispatched;
  });

  // SIMPLE FILTERING: PENDING DISPATCH = work has started
  const pendingDispatchVouchers = vouchers.filter(v => {
    return v.originalDepartment === department &&
           v.department === 'AUDIT' &&
           v.status === VOUCHER_STATUSES.AUDIT_PROCESSING &&
           v.receivedByAudit === true &&
           v.workStarted === true &&  // Work has started
           !v.dispatched;
  });
  // SIMPLE FILTERING: DISPATCHED = vouchers sent back from audit to department
  const dispatchedVouchers = vouchers.filter(v =>
    v.originalDepartment === department &&
    (v.dispatched === true || v.auditDispatchedBy || v.auditDispatchTime) &&
    v.status !== VOUCHER_STATUSES.VOUCHER_RETURNED &&
    v.status !== VOUCHER_STATUSES.VOUCHER_REJECTED
  );

  const returnedVouchers = vouchers.filter(v =>
    v.originalDepartment === department &&
    v.status === VOUCHER_STATUSES.VOUCHER_RETURNED
  );

  const rejectedVouchers = vouchers.filter(v =>
    v.originalDepartment === department &&
    v.status === VOUCHER_STATUSES.VOUCHER_REJECTED
  );

  // Tab counts for debugging (removed console logging to prevent loops)

  return {
    activeTab,
    setActiveTab,
    newVouchers,
    pendingDispatchVouchers,
    dispatchedVouchers,
    returnedVouchers,
    rejectedVouchers
  };
};
