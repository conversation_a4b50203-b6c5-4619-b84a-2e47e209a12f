const axios = require('axios');

async function testApiResponse() {
  try {
    console.log('🔍 Testing API Response Structure...\n');

    // Login
    const loginResponse = await axios.post('http://localhost:8080/api/auth/login', {
      department: 'AUDIT',
      username: 'SAMUEL ASIEDU',
      password: '123'
    }, {
      withCredentials: true
    });

    const cookies = loginResponse.headers['set-cookie'];
    const cookieHeader = cookies ? cookies.join('; ') : '';

    // Test API response
    const response = await axios.get('http://localhost:8080/api/provisional-cash', {
      headers: {
        'Cookie': cookieHeader
      }
    });

    console.log('📊 Raw API Response:');
    console.log(JSON.stringify(response.data, null, 2));

    if (response.data.length > 0) {
      console.log('\n📋 First Record Analysis:');
      const firstRecord = response.data[0];
      console.log('Keys in record:', Object.keys(firstRecord));
      
      Object.keys(firstRecord).forEach(key => {
        console.log(`  ${key}: ${firstRecord[key]} (${typeof firstRecord[key]})`);
      });
    }

  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

testApiResponse();
