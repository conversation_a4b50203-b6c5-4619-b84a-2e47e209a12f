/**
 * This file defines the standard voucher status flow and transitions
 * to ensure consistency across the application.
 */

import { TransactionStatus } from '../types.js';
import { logger } from './logger.js';

// Define all possible voucher statuses
export const VOUCHER_STATUSES = {
  // Department statuses
  PENDING: 'PENDING' as TransactionStatus,
  PENDING_SUBMISSION: 'PENDING SUBMISSION' as TransactionStatus,
  PENDING_RECEIPT: 'PENDING RECEIPT' as TransactionStatus,
  VOUCHER_PROCESSING: 'VOUCHER PROCESSING' as TransactionStatus,

  // Audit statuses
  AUDIT_PROCESSING: 'AUDIT: PROCESSING' as TransactionStatus,

  // Final statuses
  VOUCHER_CERTIFIED: 'VOUCHER CERTIFIED' as TransactionStatus,
  VOUCHER_REJECTED: 'VOUCHER REJECTED' as TransactionStatus,
  REJECTED_PENDING_DISPATCH: 'REJECTED: PENDING DISPATCH' as TransactionStatus,
  VO<PERSON>HER_RETURNED: 'VOUCHER RETURNED' as TransactionStatus,

  // Other statuses
  VOUCHER_PENDING_RETURN: 'VOUCHER PENDING RETURN' as TransactionStatus,

  // System statuses
  OFFSET_BY_AUDIT: 'OFFSET_BY_AUDIT' as TransactionStatus,
} as const;

// Define voucher flags interface
export interface VoucherFlags {
  sentToAudit: boolean;
  dispatched: boolean;
  certified: boolean;
  rejected: boolean;
  isReturned: boolean;
  pendingReturn: boolean;
  receivedByAudit: boolean;
}

// COMPREHENSIVE SOLUTION: Define valid status transitions with metadata
export const VALID_STATUS_TRANSITIONS = {
  // PENDING can transition to PENDING_RECEIPT when sent to audit
  [VOUCHER_STATUSES.PENDING]: {
    allowedTransitions: [VOUCHER_STATUSES.PENDING_RECEIPT],
    requiredFlags: { sentToAudit: true } as Partial<VoucherFlags>,
    clearFlags: [],
    requiredRole: ['admin', 'USER'] as const // FIXED: USER role can send vouchers to audit
  },
  // PENDING_SUBMISSION can also transition to PENDING_RECEIPT (backward compatibility)
  [VOUCHER_STATUSES.PENDING_SUBMISSION]: {
    allowedTransitions: [VOUCHER_STATUSES.PENDING_RECEIPT],
    requiredFlags: { sentToAudit: true } as Partial<VoucherFlags>,
    clearFlags: [],
    requiredRole: ['admin', 'USER'] as const // FIXED: USER role can send vouchers to audit
  },
  [VOUCHER_STATUSES.PENDING_RECEIPT]: {
    allowedTransitions: [VOUCHER_STATUSES.AUDIT_PROCESSING],
    requiredFlags: {} as Partial<VoucherFlags>, // CRITICAL FIX: Don't require receivedByAudit flag - it gets set during transition
    clearFlags: [],
    requiredRole: ['AUDIT', 'SYSTEM ADMIN'] as const
  },
  [VOUCHER_STATUSES.AUDIT_PROCESSING]: {
    allowedTransitions: [
      VOUCHER_STATUSES.VOUCHER_CERTIFIED,
      VOUCHER_STATUSES.VOUCHER_REJECTED,
      VOUCHER_STATUSES.REJECTED_PENDING_DISPATCH,
      VOUCHER_STATUSES.VOUCHER_PENDING_RETURN,
      VOUCHER_STATUSES.VOUCHER_RETURNED
    ],
    requiredFlags: {} as Partial<VoucherFlags>,
    clearFlags: [],
    requiredRole: ['AUDIT', 'SYSTEM ADMIN'] as const
  },
  [VOUCHER_STATUSES.VOUCHER_PENDING_RETURN]: {
    allowedTransitions: [VOUCHER_STATUSES.VOUCHER_RETURNED],
    requiredFlags: { pendingReturn: true } as Partial<VoucherFlags>,
    clearFlags: ['pendingReturn'],
    requiredRole: ['AUDIT', 'SYSTEM ADMIN'] as const
  },
  [VOUCHER_STATUSES.VOUCHER_CERTIFIED]: {
    allowedTransitions: [VOUCHER_STATUSES.VOUCHER_PROCESSING],
    requiredFlags: { dispatched: true } as Partial<VoucherFlags>,
    clearFlags: [],
    requiredRole: ['admin', 'USER'] as const // FIXED: USER role can handle voucher processing
  },
  [VOUCHER_STATUSES.VOUCHER_REJECTED]: {
    allowedTransitions: [VOUCHER_STATUSES.VOUCHER_PROCESSING],
    requiredFlags: { dispatched: true } as Partial<VoucherFlags>,
    clearFlags: [],
    requiredRole: ['admin', 'USER'] as const // FIXED: USER role can handle voucher processing
  },
  [VOUCHER_STATUSES.REJECTED_PENDING_DISPATCH]: {
    allowedTransitions: [VOUCHER_STATUSES.VOUCHER_REJECTED],
    requiredFlags: {} as Partial<VoucherFlags>,
    clearFlags: [],
    requiredRole: ['AUDIT', 'SYSTEM ADMIN'] as const
  },
  [VOUCHER_STATUSES.VOUCHER_RETURNED]: {
    allowedTransitions: [VOUCHER_STATUSES.VOUCHER_PROCESSING],
    requiredFlags: { dispatched: true } as Partial<VoucherFlags>,
    clearFlags: [],
    requiredRole: ['admin', 'USER'] as const // FIXED: USER role can handle voucher processing
  },
  [VOUCHER_STATUSES.VOUCHER_PROCESSING]: {
    allowedTransitions: [
      VOUCHER_STATUSES.VOUCHER_CERTIFIED,
      VOUCHER_STATUSES.VOUCHER_REJECTED,
      VOUCHER_STATUSES.VOUCHER_RETURNED,
      VOUCHER_STATUSES.PENDING_SUBMISSION
    ],
    requiredFlags: {} as Partial<VoucherFlags>,
    clearFlags: ['sentToAudit', 'dispatched', 'certified', 'rejected', 'isReturned', 'pendingReturn', 'receivedByAudit'],
    requiredRole: ['admin', 'USER'] as const // FIXED: USER role can handle voucher processing
  }
} as const;

// Validate if a status transition is allowed - DUAL ROLE SYSTEM FIX
export function isValidStatusTransition(
  currentStatus: TransactionStatus,
  newStatus: TransactionStatus,
  userRole: string,
  flags: VoucherFlags,
  userDepartment?: string
): { isValid: boolean; reason?: string } {
  const transition = VALID_STATUS_TRANSITIONS[currentStatus];

  if (!transition) {
    return { isValid: false, reason: `Invalid current status: ${currentStatus}` };
  }

  if (!transition.allowedTransitions.includes(newStatus)) {
    return {
      isValid: false,
      reason: `Cannot transition from ${currentStatus} to ${newStatus}. Allowed transitions: ${transition.allowedTransitions.join(', ')}`
    };
  }

  // DUAL ROLE SYSTEM: Check admin role gate first
  if (userRole === 'VIEWER') {
    return {
      isValid: false,
      reason: `VIEWER role cannot perform any operations. Contact admin to change your role.`
    };
  }

  // DUAL ROLE SYSTEM: Check department-based permissions for specific transitions
  if (currentStatus === VOUCHER_STATUSES.PENDING_RECEIPT && newStatus === VOUCHER_STATUSES.AUDIT_PROCESSING) {
    // This is AUDIT acceptance - check department, not admin role
    if (userDepartment !== 'AUDIT' && userRole !== 'admin') {
      return {
        isValid: false,
        reason: `Only AUDIT department can accept vouchers. User department: ${userDepartment}`
      };
    }
  } else {
    // For other transitions, check the original role requirements
    const allowedRoles = transition.requiredRole as readonly string[];
    if (!allowedRoles.includes(userRole)) {
      return {
        isValid: false,
        reason: `User role ${userRole} not authorized for this transition. Required roles: ${transition.requiredRole.join(', ')}`
      };
    }
  }

  // Check required flags
  for (const [flag, required] of Object.entries(transition.requiredFlags)) {
    if (required && !flags[flag as keyof VoucherFlags]) {
      return {
        isValid: false,
        reason: `Required flag "${flag}" not set for this transition`
      };
    }
  }

  return { isValid: true };
}

// Get the flags that should be set for a given status
export function getFlagsForStatus(status: TransactionStatus): VoucherFlags {
  const defaultFlags: VoucherFlags = {
    sentToAudit: false,
    dispatched: false,
    certified: false,
    rejected: false,
    isReturned: false,
    pendingReturn: false,
    receivedByAudit: false
  };

  switch (status) {
    case VOUCHER_STATUSES.PENDING:
      return { ...defaultFlags };

    case VOUCHER_STATUSES.PENDING_SUBMISSION:
      return { ...defaultFlags };

    case VOUCHER_STATUSES.PENDING_RECEIPT:
      return { ...defaultFlags, sentToAudit: true };

    case VOUCHER_STATUSES.AUDIT_PROCESSING:
      return { ...defaultFlags, sentToAudit: true, receivedByAudit: true };

    case VOUCHER_STATUSES.VOUCHER_CERTIFIED:
      return { ...defaultFlags, sentToAudit: true, dispatched: true, certified: true, receivedByAudit: true };

    case VOUCHER_STATUSES.VOUCHER_REJECTED:
      return { ...defaultFlags, sentToAudit: true, dispatched: true, rejected: true, receivedByAudit: true };

    case VOUCHER_STATUSES.REJECTED_PENDING_DISPATCH:
      return { ...defaultFlags, sentToAudit: true, rejected: true, receivedByAudit: true };

    case VOUCHER_STATUSES.VOUCHER_PENDING_RETURN:
      return { ...defaultFlags, sentToAudit: true, pendingReturn: true, receivedByAudit: true };

    case VOUCHER_STATUSES.VOUCHER_RETURNED:
      return { ...defaultFlags, sentToAudit: true, isReturned: true, receivedByAudit: true };

    case VOUCHER_STATUSES.VOUCHER_PROCESSING:
      return { ...defaultFlags, sentToAudit: true, dispatched: true, receivedByAudit: true };

    default:
      return { ...defaultFlags };
  }
}

// Synchronize voucher flags with its status
export function synchronizeVoucherFlags(voucher: { status: TransactionStatus; flags?: Partial<VoucherFlags> }): { status: TransactionStatus; flags: VoucherFlags } {
  const flags = getFlagsForStatus(voucher.status);
  return { status: voucher.status, flags };
}

// Get next allowed statuses based on current status and user role
export function getNextAllowedStatuses(
  currentStatus: TransactionStatus,
  userRole: string,
  flags: VoucherFlags
): TransactionStatus[] {
  const transition = VALID_STATUS_TRANSITIONS[currentStatus];
  if (!transition) return [];

  return transition.allowedTransitions.filter(newStatus => {
    const { isValid } = isValidStatusTransition(currentStatus, newStatus, userRole, flags);
    return isValid;
  });
}

// Log status transition
export function logStatusTransition(
  voucherId: string,
  fromStatus: TransactionStatus,
  toStatus: TransactionStatus,
  userId: string,
  reason?: string
): void {
  logger.info({
    message: 'Voucher status transition',
    voucherId,
    fromStatus,
    toStatus,
    userId,
    reason
  });
}
