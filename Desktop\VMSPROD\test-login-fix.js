const axios = require('axios');

async function testLoginFix() {
  console.log('🧪 TESTING LOGIN FIX');
  console.log('='.repeat(40));

  const baseURL = 'http://localhost:8080';

  try {
    // Step 1: Test server health
    console.log('\n1. SERVER HEALTH CHECK');
    console.log('-'.repeat(30));
    
    const healthResponse = await axios.get(`${baseURL}/api/health`);
    console.log('✅ Server Status:', healthResponse.data.status);
    console.log('✅ Server Time:', new Date(healthResponse.data.timestamp).toLocaleString());

    // Step 2: Test login
    console.log('\n2. TESTING LOGIN');
    console.log('-'.repeat(30));
    
    const loginData = {
      department: 'FINANCE',
      username: 'FELIX AYISI',
      password: '123'
    };

    console.log('🔐 Attempting login...');
    const loginResponse = await axios.post(`${baseURL}/api/auth/login`, loginData, { 
      withCredentials: true,
      timeout: 10000
    });

    if (loginResponse.status === 200) {
      console.log('✅ LOGIN SUCCESSFUL!');
      console.log('👤 User:', loginResponse.data.user.name);
      console.log('🏢 Department:', loginResponse.data.user.department);
      console.log('🎭 Role:', loginResponse.data.user.role);
      console.log('📅 Selected Year:', loginResponse.data.user.selectedYear);

      // Step 3: Test audit trail (if accessible)
      console.log('\n3. TESTING AUDIT TRAIL ACCESS');
      console.log('-'.repeat(30));
      
      try {
        const auditResponse = await axios.get(`${baseURL}/api/audit-trail/recent?limit=5`, {
          withCredentials: true
        });
        
        if (auditResponse.status === 200) {
          console.log('✅ Audit trail accessible');
          console.log('📊 Recent activities:', auditResponse.data.length);
        }
      } catch (auditError) {
        if (auditError.response?.status === 403) {
          console.log('ℹ️ Audit trail requires admin access (expected for regular users)');
        } else {
          console.log('⚠️ Audit trail error:', auditError.response?.status || auditError.message);
        }
      }

      // Step 4: Test logout
      console.log('\n4. TESTING LOGOUT');
      console.log('-'.repeat(30));
      
      const logoutResponse = await axios.post(`${baseURL}/api/auth/logout`, {}, { 
        withCredentials: true 
      });
      
      if (logoutResponse.status === 200) {
        console.log('✅ LOGOUT SUCCESSFUL!');
      }

    } else {
      console.log('❌ Unexpected login response status:', loginResponse.status);
    }

    console.log('\n✅ LOGIN SYSTEM TEST COMPLETE!');
    console.log('🎯 Login/logout functionality is working properly');
    console.log('🎯 Audit trail integration is stable');

  } catch (error) {
    console.error('\n❌ LOGIN TEST FAILED:');
    
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Error:', error.response.data?.error || 'Unknown error');
      
      if (error.response.status === 500) {
        console.error('\n🔧 TROUBLESHOOTING 500 ERROR:');
        console.error('   - Check server logs for detailed error');
        console.error('   - Verify database connection');
        console.error('   - Ensure audit_logs table exists');
        console.error('   - Check AuditService import');
      }
      
      if (error.response.status === 401) {
        console.error('\n🔧 TROUBLESHOOTING 401 ERROR:');
        console.error('   - Check username/password combination');
        console.error('   - Verify user exists in database');
        console.error('   - Check department name spelling');
      }
    } else {
      console.error('   Network Error:', error.message);
      console.error('\n🔧 TROUBLESHOOTING NETWORK ERROR:');
      console.error('   - Check if server is running on port 8080');
      console.error('   - Verify no firewall blocking connection');
      console.error('   - Check server startup logs');
    }
  }
}

// Run the test
testLoginFix().catch(console.error);
