import { Button } from '@/components/ui/button';
import { ArrowLeft, Lock } from 'lucide-react';
import { useVoucherHub } from './hooks/use-voucher-hub';
import { Department } from '@/lib/types';
import { HubHeader } from './hub-header';
import { SearchBar } from './search-bar';
import { VoucherTabs } from './voucher-tabs';
import { TabContent } from './tab-content';
import { SendDialog } from './send-dialog';
import { ViewVoucherModal } from './view-voucher-modal';
import { useResourceLock } from '@/hooks/use-resource-lock';
import { useAppStore } from '@/lib/store';
import { Badge } from '@/components/ui/badge';
import { useState, useEffect } from 'react';
import { toast } from '@/hooks/use-toast';

interface DepartmentVoucherHubProps {
  department: Department;
  auditUsers: string[];
  onBackToHubs: () => void;
}

export function DepartmentVoucherHub({
  department,
  auditUsers,
  onBackToHubs
}: DepartmentVoucherHubProps) {
  const currentUser = useAppStore((state) => state.currentUser);
  const isAudit = currentUser?.department === 'AUDIT';

  // Use resource lock with department-specific targeting if in Audit
  const {
    isLocked,
    isLockOwner,
    lockOwnerName,
    acquireLock,
    releaseLock,
    isEditable
  } = useResourceLock(
    'voucher-hub',
    department,
    {
      autoRelease: true,
      onLockAcquired: () => {
        console.log(`Acquired lock for ${department} voucher hub`);
        toast({
          title: "Editor Rights Acquired",
          description: `You are now editing ${department} vouchers.`,
          variant: "default",
        });
      },
      onLockReleased: () => {
        console.log(`Released lock for ${department} voucher hub`);
        toast({
          title: "Editor Rights Released",
          description: `You are no longer editing ${department} vouchers.`,
          variant: "default",
        });
      }
    },
    // Pass the target department if this is an Audit user
    isAudit ? department : undefined
  );

  // Automatically acquire editor rights when component mounts
  useEffect(() => {
    if (isAudit && !isLocked && !isLockOwner) {
      acquireLock();
    }
  }, [isAudit, isLocked, isLockOwner, acquireLock]);

  const {
    searchTerm,
    setSearchTerm,
    sortColumn,
    sortDirection,
    activeTab,
    setActiveTab,
    dispatchedBy,
    setDispatchedBy,
    customDispatchName,
    setCustomDispatchName,
    showSendDialog,
    setShowSendDialog,
    isSending,
    filteredVouchers,
    pendingDispatchVouchers,
    newVouchers,
    dispatchedVouchers,
    returnedVouchers,
    rejectedVouchers,
    viewingVoucher,
    setViewingVoucher,
    voucherEdits,
    selectedVouchers,
    setSelectedVouchers,
    handleSort,
    handleVoucherEdit,
    handleSaveVoucherEdits,
    handleSendToDepartment,
    handleConfirmSend,
    handleViewVoucher,
    handleReturnToNew,
    handleDeleteVoucher,
    handleSelectVoucher,
    handleSelectAllVouchers
  } = useVoucherHub(department);

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Button
          onClick={onBackToHubs}
          variant="outline"
          className="border-primary hover:bg-primary/10 h-10 w-10 p-0"
        >
          <ArrowLeft className="h-4 w-4 text-primary" />
        </Button>
        <div className="flex-1">
          <HubHeader department={department} />
        </div>

        {/* Simplified lock status - only show EDITING when someone is actively editing */}
        <div className="flex items-center gap-2">
          {/* Only show EDITING badge when someone has acquired edit rights */}
          {isLockOwner ? (
            <Badge variant="default" className="bg-green-600">
              <Lock className="h-3 w-3 mr-1" />
              EDITING
            </Badge>
          ) : isLocked ? (
            <Badge variant="outline" className="border-yellow-600 text-yellow-600">
              <Lock className="h-3 w-3 mr-1" />
              LOCKED BY {lockOwnerName}
            </Badge>
          ) : null}
        </div>
      </div>

      <SearchBar
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
      />

      <VoucherTabs
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        department={department}
        newVouchers={newVouchers || []}
        pendingDispatchVouchers={pendingDispatchVouchers || []}
        dispatchedVouchers={dispatchedVouchers || []}
        returnedVouchers={returnedVouchers || []}
        rejectedVouchers={rejectedVouchers || []}
      />

      <TabContent
        activeTab={activeTab}
        filteredVouchers={filteredVouchers}
        auditUsers={auditUsers}
        sortColumn={sortColumn}
        sortDirection={sortDirection}
        department={department}
        voucherEdits={voucherEdits}
        selectedVouchers={selectedVouchers}
        onSort={handleSort}
        onVoucherEdit={handleVoucherEdit}
        onSaveVoucherEdits={handleSaveVoucherEdits}
        onReturnToNew={handleReturnToNew}
        onViewVoucher={handleViewVoucher}
        onDeleteVoucher={handleDeleteVoucher}
        onSelectVoucher={handleSelectVoucher}
        onSelectAll={handleSelectAllVouchers}
        setSelectedVouchers={setSelectedVouchers}
        dispatchedBy={dispatchedBy}
        customDispatchName={customDispatchName}
        setDispatchedBy={setDispatchedBy}
        setCustomDispatchName={setCustomDispatchName}
        handleSendToDepartment={handleSendToDepartment}
        isEditable={isEditable}
      />

      {showSendDialog && pendingDispatchVouchers && (
        <SendDialog
          open={showSendDialog}
          voucherCount={selectedVouchers.length > 0 ? selectedVouchers.length : pendingDispatchVouchers.length}
          department={department}
          isLoading={isSending}
          onConfirm={handleConfirmSend}
          onCancel={() => setShowSendDialog(false)}
        />
      )}

      {viewingVoucher && (
        <ViewVoucherModal
          viewingVoucher={viewingVoucher}
          isOpen={!!viewingVoucher}
          onClose={() => setViewingVoucher(null)}
          department={department}
          onDelete={handleDeleteVoucher}
        />
      )}
    </div>
  );
}
