import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import { query } from '../database/db.js';
import { authenticate } from '../middleware/auth.js';
import { logger } from '../utils/logger.js';

export const provisionalCashRouter = express.Router();

// Apply authentication middleware to all routes
provisionalCashRouter.use(authenticate);

// Get all provisional cash records
provisionalCashRouter.get('/', async (req, res) => {
  try {
    const { department } = req.query;
    
    let records;
    if (department) {
      // Get vouchers for the specified department - use original_department to show records from vouchers that originated from this department
      const vouchers = await query('SELECT id FROM vouchers WHERE original_department = ? OR department = ?', [department, department]) as any[];
      const voucherIds = vouchers.map((v: any) => v.id);

      if (voucherIds.length === 0) {
        return res.json([]);
      }

      // Get provisional cash records for these vouchers
      records = await query(
        `SELECT * FROM provisional_cash_records WHERE voucher_id IN (${voucherIds.map(() => '?').join(',')})`,
        voucherIds
      ) as any[];
    } else if (req.user.department === 'AUDIT' || req.user.department === 'SYSTEM ADMIN') {
      // Audit and Admin see all records
      records = await query('SELECT * FROM provisional_cash_records') as any[];
    } else {
      // Other departments see only their records - use original_department to show records from vouchers that originated from this department
      const vouchers = await query('SELECT id FROM vouchers WHERE original_department = ? OR department = ?', [req.user.department, req.user.department]) as any[];
      const voucherIds = vouchers.map((v: any) => v.id);

      if (voucherIds.length === 0) {
        return res.json([]);
      }

      // Get provisional cash records for these vouchers
      records = await query(
        `SELECT * FROM provisional_cash_records WHERE voucher_id IN (${voucherIds.map(() => '?').join(',')})`,
        voucherIds
      ) as any[];
    }

    // Convert snake_case field names to camelCase for frontend
    const convertedRecords = records.map((record: any) => ({
      id: record.id,
      voucherId: record.voucher_id,
      voucherRef: record.voucher_ref,
      claimant: record.claimant,
      description: record.description,
      mainAmount: record.main_amount,
      currency: record.currency,
      amountRetired: record.amount_retired,
      clearanceRemark: record.clearance_remark,
      dateRetired: record.date_retired,
      clearedBy: record.cleared_by,
      comment: record.comment,
      date: record.date
    }));

    res.json(convertedRecords);
  } catch (error) {
    logger.error('Get provisional cash records error:', error);
    res.status(500).json({ error: 'Failed to get provisional cash records' });
  }
});

// Get provisional cash record by ID
provisionalCashRouter.get('/:id', async (req, res) => {
  try {
    const recordId = req.params.id;
    
    // Get record
    const records = await query('SELECT * FROM provisional_cash_records WHERE id = ?', [recordId]) as any[];
    
    if (records.length === 0) {
      return res.status(404).json({ error: 'Provisional cash record not found' });
    }
    
    const record = records[0];
    
    // Get voucher to check department
    const vouchers = await query('SELECT * FROM vouchers WHERE id = ?', [record.voucher_id]) as any[];
    
    if (vouchers.length === 0) {
      return res.status(404).json({ error: 'Associated voucher not found' });
    }
    
    const voucher = vouchers[0];
    
    // Check if user has access to this record
    if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN' && voucher.department !== req.user.department) {
      return res.status(403).json({ error: 'Access denied' });
    }
    
    res.json(record);
  } catch (error) {
    logger.error('Get provisional cash record error:', error);
    res.status(500).json({ error: 'Failed to get provisional cash record' });
  }
});

// Create provisional cash record
provisionalCashRouter.post('/', async (req, res) => {
  try {
    const {
      voucherId,
      voucherRef,
      claimant,
      description,
      mainAmount,
      currency,
      date
    } = req.body;
    
    // Validate required fields
    if (!voucherId || !voucherRef || !claimant || !description || !mainAmount || !currency || !date) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Check if voucher exists
    const vouchers = await query('SELECT * FROM vouchers WHERE id = ?', [voucherId]) as any[];
    
    if (vouchers.length === 0) {
      return res.status(404).json({ error: 'Voucher not found' });
    }
    
    const voucher = vouchers[0];
    
    // Check if user has access to create records for this voucher
    if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN') {
      return res.status(403).json({ error: 'Access denied' });
    }
    
    // Create record
    const id = uuidv4();
    await query(
      `INSERT INTO provisional_cash_records (
        id, voucher_id, voucher_ref, claimant, description, main_amount, currency, date
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [id, voucherId, voucherRef, claimant, description, mainAmount, currency, date]
    );
    
    // Update voucher
    await query(
      'UPDATE vouchers SET post_provisional_cash = TRUE WHERE id = ?',
      [voucherId]
    );
    
    // Get created record
    const records = await query('SELECT * FROM provisional_cash_records WHERE id = ?', [id]) as any[];
    
    res.status(201).json(records[0]);
  } catch (error) {
    logger.error('Create provisional cash record error:', error);
    res.status(500).json({ error: 'Failed to create provisional cash record' });
  }
});

// Update provisional cash record
provisionalCashRouter.put('/:id', async (req, res) => {
  try {
    const recordId = req.params.id;
    
    // Get record
    const records = await query('SELECT * FROM provisional_cash_records WHERE id = ?', [recordId]) as any[];
    
    if (records.length === 0) {
      return res.status(404).json({ error: 'Provisional cash record not found' });
    }
    
    const record = records[0];
    
    // Get voucher to check department
    const vouchers = await query('SELECT * FROM vouchers WHERE id = ?', [record.voucher_id]) as any[];
    
    if (vouchers.length === 0) {
      return res.status(404).json({ error: 'Associated voucher not found' });
    }
    
    const voucher = vouchers[0];
    
    // Check if user has access to update this record
    if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN') {
      return res.status(403).json({ error: 'Access denied' });
    }
    
    // Build update query
    let updateQuery = 'UPDATE provisional_cash_records SET ';
    const updateParams = [];
    const updates = [];
    
    // Process each field in the request body
    for (const [key, value] of Object.entries(req.body)) {
      // Convert camelCase to snake_case for database column names
      const columnName = key.replace(/([A-Z])/g, '_$1').toLowerCase();
      updates.push(`${columnName} = ?`);
      updateParams.push(value);
    }
    
    // If no updates, return early
    if (updates.length === 0) {
      return res.status(400).json({ error: 'No updates provided' });
    }
    
    updateQuery += updates.join(', ') + ' WHERE id = ?';
    updateParams.push(recordId);
    
    // Execute update
    await query(updateQuery, updateParams);
    
    // Get updated record
    const updatedRecords = await query('SELECT * FROM provisional_cash_records WHERE id = ?', [recordId]) as any[];
    const updatedRecord = updatedRecords[0];

    // Convert snake_case to camelCase for consistency with GET endpoint
    const convertedRecord = {
      id: updatedRecord.id,
      voucherId: updatedRecord.voucher_id,
      voucherRef: updatedRecord.voucher_ref,
      claimant: updatedRecord.claimant,
      description: updatedRecord.description,
      mainAmount: updatedRecord.main_amount,
      currency: updatedRecord.currency,
      amountRetired: updatedRecord.amount_retired,
      clearanceRemark: updatedRecord.clearance_remark,
      dateRetired: updatedRecord.date_retired,
      clearedBy: updatedRecord.cleared_by,
      comment: updatedRecord.comment,
      date: updatedRecord.date
    };

    res.json(convertedRecord);
  } catch (error) {
    logger.error('Update provisional cash record error:', error);
    res.status(500).json({ error: 'Failed to update provisional cash record' });
  }
});

// Delete provisional cash record
provisionalCashRouter.delete('/:id', async (req, res) => {
  try {
    const recordId = req.params.id;
    
    // Get record
    const records = await query('SELECT * FROM provisional_cash_records WHERE id = ?', [recordId]) as any[];
    
    if (records.length === 0) {
      return res.status(404).json({ error: 'Provisional cash record not found' });
    }
    
    const record = records[0];
    
    // Get voucher to check department
    const vouchers = await query('SELECT * FROM vouchers WHERE id = ?', [record.voucher_id]) as any[];
    
    if (vouchers.length === 0) {
      return res.status(404).json({ error: 'Associated voucher not found' });
    }
    
    const voucher = vouchers[0];
    
    // Check if user has access to delete this record
    if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN') {
      return res.status(403).json({ error: 'Access denied' });
    }
    
    // Delete record
    await query('DELETE FROM provisional_cash_records WHERE id = ?', [recordId]);
    
    // Update voucher if no more records exist for it
    const remainingRecords = await query(
      'SELECT COUNT(*) as count FROM provisional_cash_records WHERE voucher_id = ?',
      [record.voucher_id]
    ) as any[];
    
    if (remainingRecords[0].count === 0) {
      await query(
        'UPDATE vouchers SET post_provisional_cash = FALSE WHERE id = ?',
        [record.voucher_id]
      );
    }
    
    res.json({ message: 'Provisional cash record deleted successfully' });
  } catch (error) {
    logger.error('Delete provisional cash record error:', error);
    res.status(500).json({ error: 'Failed to delete provisional cash record' });
  }
});
