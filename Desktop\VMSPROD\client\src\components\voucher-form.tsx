import { useState, useRef } from 'react';
import { formatCurrentDate } from '@/utils/formatUtils';
import { Department, Currency } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { useAppStore } from '@/lib/store';
import { currencies } from '@/lib/data';
import { toast } from 'sonner';
import { AlertCircle } from 'lucide-react';

interface VoucherFormProps {
  department: Department;
  onSubmit?: () => void;
  onComplete?: () => void;
  isDisabled?: boolean;
  infoMessage?: string;
}

export function VoucherForm({
  department,
  onSubmit,
  onComplete,
  isDisabled = false,
  infoMessage
}: VoucherFormProps) {
  const currentUser = useAppStore((state) => state.currentUser);
  const addVoucher = useAppStore((state) => state.addVoucher);

  const [claimant, setClaimant] = useState('');
  const [description, setDescription] = useState('');
  const [amount, setAmount] = useState('');
  const [currency, setCurrency] = useState<Currency>("GHS");
  const [createdVoucherId, setCreatedVoucherId] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // PRODUCTION: Prevent double submissions with ref
  const submissionRef = useRef(false);
  const lastSubmissionRef = useRef<number>(0);
  const lastSubmissionContent = useRef<string>('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // PRODUCTION: Enhanced submission protection against multiplication
    const now = Date.now();

    // Check if form is disabled or already submitting
    if (isDisabled || isSubmitting) {
      console.log('Form submission blocked: disabled or already submitting');
      return;
    }

    // Check if submission is already in progress (ref-based protection)
    if (submissionRef.current) {
      console.log('Form submission blocked: submission already in progress');
      return;
    }

    // CRITICAL FIX: Prevent rapid successive submissions (increased debounce)
    if (now - lastSubmissionRef.current < 3000) { // Increased to 3 seconds
      toast.error('Please wait 3 seconds before submitting again', { duration: 3000 });
      console.log('Form submission blocked: debounce protection');
      return;
    }

    // CRITICAL FIX: Check for duplicate content in recent submissions
    const duplicateCheck = `${claimant}-${description}-${amount}`;
    if (lastSubmissionContent.current === duplicateCheck) {
      toast.error('Duplicate submission detected. Please modify the voucher details.', { duration: 4000 });
      console.log('Form submission blocked: duplicate content detected');
      return;
    }

    // Validate form fields
    if (!claimant || !description || !amount) {
      toast.error('Please fill all required fields', { duration: 3000 });
      return;
    }

    if (isNaN(Number(amount)) || Number(amount) <= 0) {
      toast.error('Amount must be a positive number', { duration: 3000 });
      return;
    }

    // Set submission flags and track content
    submissionRef.current = true;
    lastSubmissionRef.current = now;
    lastSubmissionContent.current = duplicateCheck;
    setIsSubmitting(true);

    console.log(`Starting voucher submission: ${duplicateCheck}`);

    const amountValue = parseFloat(amount);
    const date = formatCurrentDate();

    try {
      const newVoucher = await addVoucher({
        date,
        claimant,
        description,
        amount: amountValue,
        currency,
        department,
      });

      // Store the voucher ID to display it
      if (newVoucher) {
        const voucherId = newVoucher.voucher_id || newVoucher.voucherId;
        setCreatedVoucherId(voucherId);

        toast.success(`Voucher ${voucherId} created successfully. It is pending submission to Audit.`, {
          duration: 5000,
        });

        // CRITICAL FIX: Force immediate refresh of voucher data to ensure new voucher appears
        console.log('🔄 FORCING IMMEDIATE VOUCHER REFRESH AFTER CREATION');

        // Force a small delay to ensure state has updated, then trigger callbacks
        setTimeout(() => {
          if (onSubmit) {
            onSubmit();
          }

          if (onComplete) {
            onComplete();
          }
        }, 100);

        // Reset form fields but keep the voucher ID displayed
        setClaimant('');
        setDescription('');
        setAmount('');
        setCurrency("GHS");
      }
    } catch (error: any) {
      console.error('❌ Voucher creation error:', error);

      // PRODUCTION: Better error handling with specific messages
      let errorMessage = 'Failed to create voucher';

      if (error.response) {
        // Server responded with error
        const status = error.response.status;
        const data = error.response.data;

        if (status === 400) {
          errorMessage = data.error || 'Invalid voucher data. Please check all fields.';
        } else if (status === 401) {
          errorMessage = 'Authentication required. Please log in again.';
        } else if (status === 403) {
          errorMessage = 'Access denied. You do not have permission to create vouchers.';
        } else if (status === 500) {
          errorMessage = 'Server error. Please try again later.';
        } else {
          errorMessage = data.error || `Server error (${status})`;
        }
      } else if (error.request) {
        // Network error
        errorMessage = 'Network error. Please check your connection and try again.';
      } else {
        // Other error
        errorMessage = error.message || 'An unexpected error occurred';
      }

      toast.error(errorMessage, { duration: 5000 });
    } finally {
      // Always reset submission flags
      submissionRef.current = false;
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="w-full">
      {infoMessage && (
        <div className="mx-4 mt-2 p-2 bg-amber-900/30 border border-amber-700/50 rounded flex items-center text-amber-300 text-sm">
          <AlertCircle className="h-4 w-4 mr-2 text-amber-400" />
          {infoMessage}
        </div>
      )}

      <CardContent className="p-4 pb-0">
        {createdVoucherId && (
          <div className="mb-4 p-3 bg-green-900/30 border border-green-700/50 rounded flex items-center">
            <div className="text-green-300 text-sm">
              <span className="font-semibold">Voucher Created: </span>
              <span className="bg-green-950/50 px-2 py-1 rounded font-mono">{createdVoucherId}</span>
              <p className="mt-1 text-xs text-green-400">Your voucher has been created and is ready to be sent to Audit.</p>
            </div>
          </div>
        )}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div className="space-y-1">
            <Label htmlFor="claimant" className="text-xs">CLAIMANT</Label>
            <Input
              id="claimant"
              value={claimant}
              onChange={(e) => setClaimant(e.target.value)}
              placeholder="Enter claimant name"
              required
              disabled={isDisabled}
              className="h-9 text-sm"
            />
          </div>
          <div className="space-y-1">
            <Label htmlFor="amount" className="text-xs">AMOUNT</Label>
            <div className="flex gap-2">
              <Input
                id="amount"
                type="number"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder="Enter amount (e.g., 200.012)"
                min="0"
                step="0.001"
                required
                disabled={isDisabled}
                className="flex-1 h-9 text-sm"
              />
              <Select
                value={currency}
                onValueChange={(value) => setCurrency(value as Currency)}
                defaultValue="GHS"
              >
                <SelectTrigger className="w-[80px] h-9 text-sm">
                  <SelectValue placeholder="Currency" />
                </SelectTrigger>
                <SelectContent>
                  {currencies.map((c) => (
                    <SelectItem key={c} value={c}>{c}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <div className="space-y-1 mb-4">
          <Label htmlFor="description" className="text-xs">DESCRIPTION</Label>
          <Textarea
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Enter voucher description"
            required
            disabled={isDisabled}
            className="h-20 text-sm min-h-[60px]"
          />
        </div>
      </CardContent>
      <CardFooter className="pt-0 pb-2">
        <Button
          type="submit"
          variant="success"
          className="w-40 h-8 text-sm"
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Creating...' : 'Create Voucher'}
        </Button>
      </CardFooter>
    </form>
  );
}
