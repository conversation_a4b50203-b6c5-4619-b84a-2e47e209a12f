
import { useNavigate } from 'react-router-dom';
import { useAppStore } from '@/lib/store';
import { useDashboardState } from '@/hooks/use-dashboard-state';
import { useSmartBatchLocking } from '@/hooks/use-smart-batch-locking';
import { useNetworkStatus } from '@/hooks/use-network-status';
import { DashboardHeader } from '@/components/dashboard/dashboard-header';
import { NewVoucherForm } from '@/components/dashboard/new-voucher-form';
import { DashboardContent } from '@/components/dashboard/dashboard-content';
import { DashboardModals } from '@/components/dashboard/dashboard-modals';
import { DashboardFooter } from '@/components/dashboard/dashboard-footer';
import { OfflineStatus } from '@/components/offline-status';
import { Department } from '@/lib/types';
import { useDepartmentData } from '@/hooks/use-department-data';
import { useEffect } from 'react';

export default function Dashboard() {
  const navigate = useNavigate();
  const fetchBatches = useAppStore((state) => state.fetchBatches);

  // Initialize network status monitoring
  useNetworkStatus();
  const {
    currentUser,
    selectedVouchers,
    setSelectedVouchers,
    showVoucherReceiving,
    setShowVoucherReceiving,
    receivingVoucherIds,
    setReceivingVoucherIds,
    dispatchedBy,
    setDispatchedBy,
    customDispatchName,
    setCustomDispatchName,
    viewingVoucher,
    setViewingVoucher,
    showBatchReceiving,
    setShowBatchReceiving,
    selectedBatchId,
    setSelectedBatchId,
    voucherView,
    setVoucherView,
    isNotificationBlinking,
    refreshTrigger,
    refreshData,
    handleDisabledFormClick
  } = useDashboardState();

  // SMART BATCH LOCKING: Initialize for Finance department
  const { executeWithBatchLock, isOperationInProgress } = useSmartBatchLocking(
    currentUser?.department || 'FINANCE'
  );

  // CRITICAL FIX: Fetch batches when Finance dashboard loads
  useEffect(() => {
    if (currentUser && currentUser.department !== 'AUDIT') {
      console.log(`🔄 FINANCE: Fetching batches for department: ${currentUser.department}`);
      fetchBatches().catch((error) => {
        console.error('❌ FINANCE: Failed to fetch batches:', error);
      });
    }
  }, [currentUser?.id, fetchBatches]);

  const { batchesArray } = useDepartmentData(currentUser?.department as Department, refreshTrigger);

  const hasVouchersToReceive = batchesArray && batchesArray.length > 0 &&
    batchesArray.some(batch =>
      batch.vouchers && batch.vouchers.some(v => v.certifiedBy || v.status === "VOUCHER REJECTED")
    );

  if (!currentUser) {
    return null;
  }

  return (
    <div className="flex flex-col h-screen bg-black text-white">
      <DashboardHeader />

      <div className="px-6 py-2 bg-black">
        <NewVoucherForm
          department={currentUser.department}
          isDisabled={false}
          onDisabledClick={handleDisabledFormClick}
          hidden={false}
        />
      </div>

      <DashboardContent
        department={currentUser.department}
        refreshTrigger={refreshTrigger}
        onRefresh={refreshData}
        onReceiveVouchers={(voucherIdsOrBatchId, isBatchId = false) => {
          console.log("🔄 DASHBOARD: Receive vouchers called with:", voucherIdsOrBatchId, "isBatchId:", isBatchId);

          if (isBatchId) {
            // PRODUCTION FIX: Use proper batch receiving for batches from Audit
            console.log("✅ DASHBOARD: Opening batch receiving for batch ID:", voucherIdsOrBatchId);
            setSelectedBatchId(voucherIdsOrBatchId);
            setShowBatchReceiving(true);
          } else {
            // Fallback to old voucher receiving for individual vouchers
            console.log("⚠️ DASHBOARD: Using fallback voucher receiving for voucher IDs:", voucherIdsOrBatchId);
            setReceivingVoucherIds(voucherIdsOrBatchId);
            setShowVoucherReceiving(true);
          }
        }}
        selectedVouchers={selectedVouchers}
        dispatchedBy={dispatchedBy}
        customDispatchName={customDispatchName}
        onDispatcherChange={setDispatchedBy}
        onCustomDispatchNameChange={setCustomDispatchName}
        onSendToAudit={async () => {
          const sendVouchersToAudit = useAppStore.getState().sendVouchersToAudit;

          if (selectedVouchers.length > 0 && (dispatchedBy || customDispatchName)) {
            const finalDispatchedBy = dispatchedBy || customDispatchName.toUpperCase();

            // SMART BATCH LOCKING: Execute batch dispatch with automatic locking
            const result = await executeWithBatchLock(
              'batch-dispatch',
              async () => {
                return await sendVouchersToAudit(currentUser.department, selectedVouchers, finalDispatchedBy);
              },
              selectedVouchers
            );

            // Only update UI if operation was successful (not blocked by lock)
            if (result !== null) {
              setSelectedVouchers([]);
              setDispatchedBy('');
              setCustomDispatchName('');
              setVoucherView('processing');
            }
          }
        }}
        onSelectionChange={setSelectedVouchers}
        onViewVoucher={setViewingVoucher}
        voucherView={voucherView}
        onVoucherViewChange={setVoucherView}
        isNotificationBlinking={isNotificationBlinking}
      />

      <DashboardModals
        department={currentUser.department}
        viewingVoucher={viewingVoucher}
        setViewingVoucher={setViewingVoucher}
        showVoucherReceiving={showVoucherReceiving}
        setShowVoucherReceiving={setShowVoucherReceiving}
        receivingVoucherIds={receivingVoucherIds}
        showBatchReceiving={showBatchReceiving}
        setShowBatchReceiving={setShowBatchReceiving}
        selectedBatchId={selectedBatchId}
        onRefresh={refreshData}
      />
      <DashboardFooter />
    </div>
  );
}
