import { Button } from '@/components/ui/button';
import { formatNumberWithCommas, formatVMSDateTime } from '@/utils/formatUtils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import React, { useRef, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { type Voucher } from '@/lib/types';
import { ScrollArea } from '@/components/ui/scroll-area';
import { SortableColumnHeader } from './sortable-column-header';
import { cn } from '@/lib/utils';
import { ArrowUpDown, CornerUpLeft } from 'lucide-react';

type CheckedState = boolean | 'indeterminate';

interface PendingDispatchTabProps {
  filteredVouchers: Voucher[];
  sortColumn: string | null;
  sortDirection: 'asc' | 'desc';
  handleSort: (column: string) => void;
  dispatchedBy?: string;
  customDispatchName?: string;
  selectedVouchers: string[];
  handleSelectVoucher: (voucherId: string) => void;
  handleSelectAll: (checked: boolean) => void;
  setDispatchedBy?: (value: string) => void;
  setCustomDispatchName?: (value: string) => void;
  handleSendToDepartment?: () => void;
  setSelectedVouchers?: (vouchers: string[]) => void;
  isAudit?: boolean;
  handleReturnToNew?: (voucherId: string) => void;
  auditUsers?: string[];
}

export function PendingDispatchTab({
  filteredVouchers,
  sortColumn,
  sortDirection,
  handleSort,
  selectedVouchers,
  handleSelectVoucher,
  handleSelectAll,
  handleSendToDepartment,
  handleReturnToNew,
  isAudit,
}: PendingDispatchTabProps) {
  // Calculate if all vouchers are selected
  const isAllSelected =
    filteredVouchers.length > 0 &&
    selectedVouchers.length === filteredVouchers.length;

  const onSelectAll = (checked: CheckedState) => {
    handleSelectAll(checked === true);
  };

  const onSelectVoucher = (voucherId: string) => (checked: CheckedState) => {
    if (checked === true || checked === false) {
      handleSelectVoucher(voucherId);
    }
  };

  // Refs for synchronized scrolling
  const headerRef = useRef<HTMLDivElement>(null);
  const bodyRef = useRef<HTMLDivElement>(null);

  // Set up synchronized scrolling between header and body
  useEffect(() => {
    const headerElement = headerRef.current;
    const bodyElement = bodyRef.current;

    if (!headerElement || !bodyElement) return;

    const handleHeaderScroll = () => {
      if (bodyElement) {
        bodyElement.scrollLeft = headerElement.scrollLeft;
      }
    };

    const handleBodyScroll = () => {
      if (headerElement) {
        headerElement.scrollLeft = bodyElement.scrollLeft;
      }
    };

    headerElement.addEventListener('scroll', handleHeaderScroll);
    bodyElement.addEventListener('scroll', handleBodyScroll);

    return () => {
      headerElement.removeEventListener('scroll', handleHeaderScroll);
      bodyElement.removeEventListener('scroll', handleBodyScroll);
    };
  }, []);

  return (
    <div className="space-y-4">
      <div className="flex justify-end">
        {handleSendToDepartment && (
          <Button
            onClick={handleSendToDepartment}
            disabled={filteredVouchers.length === 0}
            variant="success"
            className="uppercase"
          >
            SEND TO DEPARTMENT ({filteredVouchers.length})
          </Button>
        )}
      </div>

      <div className="flex flex-col rounded-md border">
        {/* Fixed header */}
        <div className="sticky top-0 z-10 bg-background overflow-hidden">
          <div ref={headerRef} className="overflow-x-auto scrollbar-visible" style={{ scrollbarWidth: 'thin' }}>
            <table className="w-full table-fixed" style={{ tableLayout: 'fixed', minWidth: '1500px' }}>
              <thead className="sticky top-0 z-10 bg-background">
                <tr className="bg-background">
                  <th className="w-[5%] sticky left-0 z-20 bg-background p-4 text-center">
                    <Checkbox
                      checked={isAllSelected}
                      onCheckedChange={onSelectAll}
                      aria-label="Select all vouchers"
                    />
                  </th>
                  <th className="sticky left-[50px] z-20 bg-background w-[15%] p-4 text-center font-medium uppercase whitespace-nowrap">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('voucherId')}
                    >
                      <span>VOUCHER ID</span>
                      {sortColumn === 'voucherId' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[20%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('date')}
                    >
                      <span>DATE</span>
                      {sortColumn === 'date' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[20%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('claimant')}
                    >
                      <span>CLAIMANT</span>
                      {sortColumn === 'claimant' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[25%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('description')}
                    >
                      <span>DESCRIPTION</span>
                      {sortColumn === 'description' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[10%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('amount')}
                    >
                      <span>AMOUNT</span>
                      {sortColumn === 'amount' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[15%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('preAuditedAmount')}
                    >
                      <span>CERTIFIED AMT</span>
                      {sortColumn === 'preAuditedAmount' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[12%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('preAuditedBy')}
                    >
                      <span>PRE-AUDITED BY</span>
                      {sortColumn === 'preAuditedBy' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[12%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('certifiedBy')}
                    >
                      <span>CERTIFIED BY</span>
                      {sortColumn === 'certifiedBy' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[15%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">STATUS</th>
                  <th className="w-[20%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">COMMENT</th>
                  <th className="w-[5%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">ACTION</th>
                </tr>
              </thead>
            </table>
          </div>
        </div>

        {/* Scrollable body */}
        <div className="overflow-auto h-[60vh] scrollbar-visible" style={{ scrollbarWidth: 'thin', overflowY: 'scroll' }}>
          <div ref={bodyRef} className="overflow-x-auto scrollbar-visible" style={{ scrollbarWidth: 'thin' }}>
            <table className="w-full table-fixed" style={{ tableLayout: 'fixed', minWidth: '1500px' }}>
              <tbody>
              {filteredVouchers.length === 0 ? (
                <tr className="border-b h-14">
                  <td colSpan={12} className="p-4 text-center uppercase font-medium">
                    NO PENDING DISPATCH VOUCHERS FOUND.
                  </td>
                </tr>
              ) : (
                filteredVouchers.map((voucher) => (
                  <tr key={voucher.id} className="border-b h-14">
                    <td className="sticky left-0 bg-background z-10 p-4 align-middle w-[5%] text-center">
                      <Checkbox
                        checked={selectedVouchers.includes(voucher.id)}
                        onCheckedChange={onSelectVoucher(voucher.id)}
                        aria-label={`Select voucher ${voucher.voucherId}`}
                      />
                    </td>
                    <td className="font-medium uppercase sticky left-[50px] bg-background z-10 p-4 align-middle w-[15%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{voucher.voucherId}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{voucher.voucherId}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase p-4 align-middle w-[20%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{formatVMSDateTime(voucher.date)}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{formatVMSDateTime(voucher.date)}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase p-4 align-middle w-[20%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{voucher.claimant}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{voucher.claimant}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="max-w-xs truncate uppercase p-4 align-middle w-[25%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{voucher.description}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{voucher.description}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase whitespace-nowrap p-4 align-middle w-[10%] text-center text-xs">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">
                              {formatNumberWithCommas(voucher.amount)} {voucher.currency}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{formatNumberWithCommas(voucher.amount)} {voucher.currency}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase whitespace-nowrap p-4 align-middle w-[15%] text-center text-xs">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">
                              {voucher.preAuditedAmount ? `${formatNumberWithCommas(voucher.preAuditedAmount)} ${voucher.currency}` : '-'}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{voucher.preAuditedAmount ? `${formatNumberWithCommas(voucher.preAuditedAmount)} ${voucher.currency}` : '-'}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase p-4 align-middle w-[12%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{voucher.preAuditedBy || '-'}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="text-center">
                              <p className="font-semibold">Pre-Audited By</p>
                              <p>{voucher.preAuditedBy || 'Not pre-audited'}</p>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase p-4 align-middle w-[12%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{voucher.certifiedBy || '-'}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="text-center">
                              <p className="font-semibold">Certified By</p>
                              <p>{voucher.certifiedBy || 'Not certified'}</p>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block">
                              <span className={cn(
                                "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 uppercase",
                                voucher.pendingReturn
                                  ? "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80"
                                  : voucher.status === "REJECTED: PENDING DISPATCH" || voucher.isRejectedVoucher
                                    ? "border-transparent bg-red-600 text-white hover:bg-red-700"
                                    : "text-foreground"
                              )}>
                                {voucher.pendingReturn
                                  ? "PENDING RETURN"
                                  : voucher.status === "REJECTED: PENDING DISPATCH" || voucher.isRejectedVoucher
                                    ? "REJECTED: PENDING DISPATCH"
                                    : "PENDING DISPATCH"}
                              </span>
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{voucher.pendingReturn
                              ? "PENDING RETURN"
                              : voucher.status === "REJECTED: PENDING DISPATCH" || voucher.isRejectedVoucher
                                ? "REJECTED: PENDING DISPATCH"
                                : "PENDING DISPATCH"}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase max-w-[250px] truncate p-4 align-middle w-[20%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">
                              {voucher.returnComment || voucher.comment || (voucher.pendingReturn ? "NO COMMENT PROVIDED" : "")}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{voucher.returnComment || voucher.comment || (voucher.pendingReturn ? "NO COMMENT PROVIDED" : "")}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="p-4 align-middle w-[5%] text-center">
                      {handleReturnToNew && (
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleReturnToNew(voucher.id);
                                }}
                                className="h-8 w-8 text-blue-500 hover:text-blue-700 hover:bg-blue-100/50"
                              >
                                <CornerUpLeft className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Return to New Vouchers</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      )}
                    </td>
                  </tr>
                ))
              )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
