
import { useAppStore } from '@/lib/store';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { User } from '@/lib/types';
import { Lock } from 'lucide-react';

interface VoucherListControlsProps {
  voucherView: string;
  selectedVouchers: string[];
  dispatchedBy: string;
  customDispatchName: string;
  departmentUsers: User[];
  onSendToAudit: () => void;
  onDispatcherChange: (value: string) => void;
  onCustomDispatchNameChange: (value: string) => void;
  isDisabled?: boolean;
}

export function VoucherListControls({
  voucherView,
  selectedVouchers,
  dispatchedBy,
  customDispatchName,
  departmentUsers,
  onSendToAudit,
  onDispatcherChange,
  onCustomDispatchNameChange,
  isDisabled = false
}: VoucherListControlsProps) {
  const currentUser = useAppStore((state) => state.currentUser);
  const users = useAppStore((state) => state.users);

  // Filter users to only include those from the current department
  const departmentUsersList = users.filter(user =>
    user.department === currentUser?.department &&
    user.name !== currentUser?.name
  );

  // Add current user to the list
  if (currentUser) {
    departmentUsersList.unshift(currentUser);
  }

  // Only show the send controls for the "pending-submission" tab when not disabled
  if (voucherView !== "pending-submission" || isDisabled) return null;

  // Debug log to check users
  console.log('Department users for dispatch dropdown:', departmentUsersList.map(u => u.name));

  return (
    <div className={`flex items-center justify-between mb-4 ${isDisabled ? 'opacity-70 pointer-events-none' : ''}`}>
      <div className="flex items-center gap-2">
        <span className="text-sm text-amber-300 uppercase whitespace-nowrap">
          Dispatched By:
        </span>
        <Select
          value={dispatchedBy}
          onValueChange={onDispatcherChange}
          disabled={isDisabled}
        >
          <SelectTrigger className="w-[180px] bg-[#0f0f0f] border-gray-700 text-white">
            <SelectValue placeholder="Select user" />
          </SelectTrigger>
          <SelectContent className="bg-[#0f0f0f] border-gray-700 text-white">
            {departmentUsersList.length > 0 ? (
              departmentUsersList.map((user) => (
                <SelectItem key={user.id} value={user.name}>
                  {user.name}
                </SelectItem>
              ))
            ) : (
              <SelectItem value="" disabled>
                No users available
              </SelectItem>
            )}
          </SelectContent>
        </Select>
        <span className="text-sm text-white uppercase whitespace-nowrap">OR</span>
        <Input
          type="text"
          placeholder="Enter name"
          value={customDispatchName}
          onChange={(e) => onCustomDispatchNameChange(e.target.value)}
          className="w-[180px] bg-[#0f0f0f] border-gray-700 text-white"
          disabled={isDisabled || !!dispatchedBy}
          onKeyDown={(e) => {
            if (e.key === 'Enter' && customDispatchName && selectedVouchers.length > 0) {
              onSendToAudit();
            }
          }}
        />
      </div>
      <Button
        onClick={onSendToAudit}
        disabled={selectedVouchers.length === 0 || (!dispatchedBy && !customDispatchName) || isDisabled}
        variant="success"
        className="uppercase"
      >
        Send to Audit ({selectedVouchers.length})
        {isDisabled && <Lock className="ml-2 h-4 w-4" />}
      </Button>
    </div>
  );
}
