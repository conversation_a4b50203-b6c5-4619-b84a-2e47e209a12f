import { useState, useEffect } from 'react';
import { Check, X, CornerDownLeft } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useAppStore } from '@/lib/store';
import { useSmartBatchLocking } from '@/hooks/use-smart-batch-locking';
import { Voucher } from '@/lib/types';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { ScrollArea } from '@/components/ui/scroll-area';
import { VoucherRejectionDialog } from './voucher-rejection-dialog';
import { batchesApi } from '@/lib/api';

interface VoucherBatchReceivingProps {
  batchId: string;
  open: boolean;
  onClose: () => void;
  isEditable?: boolean;
}

export function VoucherBatchReceiving({ batchId, open, onClose, isEditable = true }: VoucherBatchReceivingProps) {
  const voucherBatches = useAppStore((state) => state.voucherBatches);
  const receiveVoucherBatch = useAppStore((state) => state.receiveVoucherBatch);

  // State for batch data and vouchers
  const [batch, setBatch] = useState<any>(null);
  const [batchVouchers, setBatchVouchers] = useState<Voucher[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const [acceptedVouchers, setAcceptedVouchers] = useState<string[]>([]);
  const [rejectedVouchers, setRejectedVouchers] = useState<string[]>([]);
  const [rejectionComments, setRejectionComments] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // For rejection dialog
  const [rejectionDialogOpen, setRejectionDialogOpen] = useState(false);
  const [currentVoucherId, setCurrentVoucherId] = useState<string | null>(null);

  // Fetch batch details when component opens
  useEffect(() => {
    if (open && batchId) {
      fetchBatchDetails();
    }
  }, [open, batchId]);

  const fetchBatchDetails = async () => {
    setIsLoading(true);
    try {
      console.log(`🔍 BATCH RECEIVING: Fetching batch details for batch ${batchId}`);

      // PRODUCTION FIX: Validate batch ID format first
      if (!batchId || batchId.trim() === '') {
        throw new Error('Invalid batch ID provided');
      }

      // PRODUCTION FIX: Try API first with timeout protection
      try {
        console.log('🌐 BATCH RECEIVING: Fetching batch data from API...');

        // Add timeout protection
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('API request timeout after 10 seconds')), 10000);
        });

        const apiPromise = batchesApi.getBatchById(batchId);
        const batchData = await Promise.race([apiPromise, timeoutPromise]);

        console.log('✅ BATCH RECEIVING: Received batch data from API:', batchData);

        if (!batchData) {
          throw new Error(`Batch ${batchId} not found in API - it may have been processed or doesn't exist`);
        }

        // PRODUCTION FIX: Ensure vouchers array exists and has data
        const batchVouchers = batchData.vouchers || [];
        console.log(`📊 BATCH RECEIVING: Batch contains ${batchVouchers.length} vouchers:`,
          batchVouchers.map(v => `${v.voucherId || v.voucher_id} (${v.id})`));

        if (batchVouchers.length === 0) {
          console.warn(`⚠️ BATCH RECEIVING: Batch ${batchId} has no vouchers, trying local store...`);
          throw new Error(`Batch ${batchId} has no vouchers in API - it may have been processed already`);
        }

        // CRITICAL: Convert server voucher format to client format for proper display
        const clientVouchers = batchVouchers.map(voucher => ({
          ...voucher,
          // Ensure all required client fields are present
          voucherId: voucher.voucher_id || voucher.voucherId,
          dispatchedBy: voucher.dispatched_by || voucher.dispatchedBy || '',
          createdBy: voucher.created_by || voucher.createdBy || '',
          sentToAudit: voucher.sent_to_audit !== undefined ? Boolean(voucher.sent_to_audit) : voucher.sentToAudit,
          isReturned: voucher.is_returned !== undefined ? Boolean(voucher.is_returned) : voucher.isReturned,
          pendingReturn: voucher.pending_return !== undefined ? Boolean(voucher.pending_return) : voucher.pendingReturn,
          amount: typeof voucher.amount === 'string' ? parseFloat(voucher.amount) : voucher.amount
        }));

        setBatch(batchData);
        setBatchVouchers(clientVouchers);

        // Reset state when new batch is loaded
        setAcceptedVouchers([]);
        setRejectedVouchers([]);
        setRejectionComments({});

        console.log(`✅ BATCH RECEIVING: Successfully loaded batch with ${clientVouchers.length} vouchers for processing`);
        return;
      } catch (apiError) {
        console.error('❌ BATCH RECEIVING: API batch fetch failed:', apiError);
        console.error('❌ BATCH RECEIVING: Error details:', {
          message: apiError instanceof Error ? apiError.message : 'Unknown error',
          stack: apiError instanceof Error ? apiError.stack : undefined,
          batchId: batchId
        });

        // PRODUCTION FIX: Check if it's a 404 error (batch not found)
        if (apiError instanceof Error && (apiError.message.includes('404') || apiError.message.includes('not found'))) {
          console.log('🔍 BATCH RECEIVING: Batch not found in API, checking if it was already processed...');

          // Check if there are any vouchers that might need receiving
          const { vouchers, fetchVouchers } = useAppStore.getState();
          await fetchVouchers(); // CRITICAL FIX: Fetch ALL vouchers

          const updatedState = useAppStore.getState();
          const currentUser = updatedState.currentUser;

          // Look for vouchers that might be waiting for this batch
          const waitingVouchers = updatedState.vouchers.filter(v =>
            v.status === 'PENDING RECEIPT' ||
            v.status === 'VOUCHER PROCESSING'
          );

          if (waitingVouchers.length === 0) {
            throw new Error(`Batch ${batchId} not found and no vouchers are waiting to be received. The batch may have been processed already.`);
          }

          console.log(`📋 BATCH RECEIVING: Found ${waitingVouchers.length} vouchers that might be related to this batch`);
        }

        // FALLBACK: Try to get batch from local store
        console.log('🔄 BATCH RECEIVING: Trying local store as fallback...');
        const { voucherBatches, vouchers, fetchVouchers } = useAppStore.getState();

        // Force refresh vouchers for all departments
        console.log('🔄 BATCH RECEIVING: Refreshing voucher data...');
        await fetchVouchers(); // CRITICAL FIX: Fetch ALL vouchers

        const updatedState = useAppStore.getState();
        const localBatch = updatedState.voucherBatches.find(b => b.id === batchId);

        if (localBatch && localBatch.voucherIds && localBatch.voucherIds.length > 0) {
          console.log('📦 BATCH RECEIVING: Found batch in local store:', localBatch);

          // Get vouchers for this batch from updated local store
          const batchVouchers = updatedState.vouchers.filter(v =>
            localBatch.voucherIds.includes(v.id)
          );

          console.log(`📊 BATCH RECEIVING: Local batch contains ${batchVouchers.length} vouchers:`,
            batchVouchers.map(v => `${v.voucherId} (${v.id})`));

          const batchData = {
            ...localBatch,
            vouchers: batchVouchers,
            sent_by: localBatch.sentBy,
            sent_time: localBatch.sentTime,
            from_audit: localBatch.fromAudit
          };

          setBatch(batchData);
          setBatchVouchers(batchVouchers);

          // Reset state when new batch is loaded
          // PRODUCTION FIX: Auto-accept all vouchers from audit by default
          if (batchData.fromAudit) {
            const allVoucherIds = batchVouchers.map(v => v.id);
            setAcceptedVouchers(allVoucherIds);
            console.log(`🔧 AUTO-ACCEPT: Pre-selected ${allVoucherIds.length} vouchers from audit for acceptance`);
          } else {
            setAcceptedVouchers([]);
          }
          setRejectedVouchers([]);
          setRejectionComments({});

          console.log(`✅ BATCH RECEIVING: Successfully loaded batch from local store with ${batchVouchers.length} vouchers`);
          return;
        }

        // FINAL FALLBACK: Create a virtual batch from vouchers that need to be received
        console.log('🔄 BATCH RECEIVING: No batch found, creating virtual batch from receivable vouchers...');
        const currentUser = useAppStore.getState().currentUser;
        const allVouchers = updatedState.vouchers;

        // Find vouchers that should be receivable for this department
        const receivableVouchers = allVouchers.filter(v => {
          const isForThisDepartment = v.department === currentUser?.department;
          const isReceivable = (
            v.status === "VOUCHER PROCESSING" ||
            v.certifiedBy ||
            v.status === "VOUCHER REJECTED" ||
            v.status === "VOUCHER RETURNED" ||
            v.isReturned ||
            v.pendingReturn ||
            (v.dispatched && v.auditDispatchedBy)
          );
          const notYetReceived = !v.departmentReceiptTime;

          return isForThisDepartment && isReceivable && notYetReceived;
        });

        if (receivableVouchers.length > 0) {
          console.log(`📦 BATCH RECEIVING: Created virtual batch with ${receivableVouchers.length} receivable vouchers`);

          const virtualBatch = {
            id: batchId,
            department: currentUser?.department || 'Unknown',
            vouchers: receivableVouchers,
            sentBy: 'Audit Department',
            sentTime: new Date().toISOString(),
            fromAudit: true
          };

          setBatch(virtualBatch);
          setBatchVouchers(receivableVouchers);

          // Reset state when new batch is loaded
          // PRODUCTION FIX: Auto-accept all vouchers from audit by default
          if (virtualBatch.fromAudit) {
            const allVoucherIds = receivableVouchers.map(v => v.id);
            setAcceptedVouchers(allVoucherIds);
            console.log(`🔧 AUTO-ACCEPT: Pre-selected ${allVoucherIds.length} vouchers from audit for acceptance`);
          } else {
            setAcceptedVouchers([]);
          }
          setRejectedVouchers([]);
          setRejectionComments({});

          console.log(`✅ BATCH RECEIVING: Successfully created virtual batch with ${receivableVouchers.length} vouchers`);
          return;
        }

        throw new Error(`No receivable vouchers found for batch ${batchId} - all vouchers may have been processed already`);
      }
    } catch (error) {
      console.error('❌ BATCH RECEIVING: Error fetching batch details:', error);

      // PRODUCTION FIX: Provide more specific error messages
      let errorMessage = 'Failed to load batch details';
      if (error instanceof Error) {
        if (error.message.includes('404') || error.message.includes('not found')) {
          errorMessage = `Batch ${batchId} not found. It may have been processed already or doesn't exist.`;
        } else if (error.message.includes('no vouchers')) {
          errorMessage = `Batch ${batchId} has no vouchers to process. It may have been completed already.`;
        } else {
          errorMessage = `Error loading batch: ${error.message}`;
        }
      }

      toast.error(errorMessage, {
        duration: 5000,
        action: {
          label: 'Close',
          onClick: () => onClose(),
        },
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!batch && !isLoading) return null;

  const handleAcceptVoucher = (voucherId: string) => {
    setAcceptedVouchers(prev => {
      if (prev.includes(voucherId)) {
        return prev.filter(id => id !== voucherId);
      } else {
        // Remove from rejected if it's there
        setRejectedVouchers(prev => prev.filter(id => id !== voucherId));
        return [...prev, voucherId];
      }
    });
  };

  const handleStartRejectVoucher = (voucherId: string) => {
    setCurrentVoucherId(voucherId);
    setRejectionDialogOpen(true);
  };

  const handleConfirmReject = (comment: string) => {
    if (currentVoucherId) {
      // Add to rejected vouchers
      setRejectedVouchers(prev => {
        if (!prev.includes(currentVoucherId)) {
          // Remove from accepted if it's there
          setAcceptedVouchers(prev => prev.filter(id => id !== currentVoucherId));
          return [...prev, currentVoucherId];
        }
        return prev;
      });

      // Save the rejection comment
      setRejectionComments(prev => ({
        ...prev,
        [currentVoucherId]: comment
      }));

      // Close the dialog
      setRejectionDialogOpen(false);
      setCurrentVoucherId(null);
    }
  };

  const handleCompleteBatchReceiving = async () => {
    const unprocessedVouchers = batchVouchers.filter(
      v => !acceptedVouchers.includes(v.id) && !rejectedVouchers.includes(v.id)
    );

    if (unprocessedVouchers.length > 0) {
      toast.error('Please accept or reject all vouchers in the batch', {
        duration: 3000,
      });
      return;
    }

    // Verify that all rejected vouchers have comments
    const missingComments = rejectedVouchers.filter(id => !rejectionComments[id] || rejectionComments[id].trim() === '');
    if (missingComments.length > 0) {
      toast.error('Please provide rejection comments for all rejected vouchers', {
        duration: 3000,
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Before calling receiveVoucherBatch, add the comments to each rejected voucher
      const finalRejectedVouchers = rejectedVouchers.map(id => {
        const comment = rejectionComments[id];
        return { id, comment };
      });

      // Update the receiveVoucherBatch function to process these comments
      await receiveVoucherBatch(
        batchId,
        acceptedVouchers,
        finalRejectedVouchers.map(item => item.id),
        rejectionComments
      );

      toast.success(`Vouchers processed: ${acceptedVouchers.length} accepted, ${rejectedVouchers.length} rejected`, {
        duration: 3000,
      });

      onClose();
    } catch (error) {
      toast.error('Failed to process vouchers', {
        duration: 3000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className="bg-black text-white border-white/10 max-w-4xl">
          <DialogHeader className="pb-2 shrink-0">
            <DialogTitle className="text-base text-white">Receive Voucher Batch</DialogTitle>
            <DialogDescription className="text-xs text-gray-400">
              {isLoading ? 'Loading batch details...' : `Review and process vouchers from ${batch?.department || 'Unknown'} Department`}
            </DialogDescription>
          </DialogHeader>

          {!isLoading && batch && (
            <div className="flex justify-between items-center py-1 px-1 shrink-0">
              <div className="text-xs text-gray-400">
                Sent by: {batch.sentBy} on {batch.sentTime}
              </div>
              <div className="flex gap-2">
                <Badge variant="success" className="flex gap-1 items-center text-xs py-0.5">
                  <Check className="h-3 w-3" /> {acceptedVouchers.length}
                </Badge>
                <Badge variant="destructive" className="flex gap-1 items-center text-xs py-0.5">
                  <X className="h-3 w-3" /> {rejectedVouchers.length}
                </Badge>
                <Badge variant="outline" className="flex gap-1 items-center text-xs py-0.5">
                  {batchVouchers.length - acceptedVouchers.length - rejectedVouchers.length}
                </Badge>
              </div>
            </div>
          )}

          <div className="overflow-y-auto flex-1 min-h-0">
            <div className="space-y-2 p-1">
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-gray-400">Loading voucher details...</div>
                </div>
              ) : batchVouchers.length === 0 ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-gray-400">No vouchers found in this batch</div>
                </div>
              ) : (
                batchVouchers.map((voucher) => {
                  const isAccepted = acceptedVouchers.includes(voucher.id);
                  const isRejected = rejectedVouchers.includes(voucher.id);
                  const isReturning = voucher.pendingReturn || voucher.isReturned;
                  const isRejectedVoucher = voucher.isRejectedVoucher || voucher.status === "VOUCHER REJECTED";

                return (
                  <div
                    key={voucher.id}
                    className={`py-3 px-4 mb-2 rounded-lg border border-white/10 ${
                      isAccepted ? 'bg-green-950/20' :
                      isRejected ? 'bg-red-950/20' :
                      isReturning ? 'bg-yellow-950/20' : 'bg-[#0f0f0f]'
                    }`}
                  >
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <div className="flex items-center gap-2">
                          <h3 className="text-base font-semibold text-white">{voucher.voucherId}</h3>
                          {isReturning && (
                            <Badge variant="outline" className="bg-yellow-900/30 text-yellow-300 border-yellow-600">
                              <CornerDownLeft className="h-3 w-3 mr-1" /> RETURNING
                            </Badge>
                          )}
                          {isRejectedVoucher && (
                            <Badge variant="outline" className="bg-red-900/30 text-red-300 border-red-600">
                              <X className="h-3 w-3 mr-1" /> REJECTED VOUCHER
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-white/70">Claimant: {voucher.claimant}</p>
                        <p className="text-sm text-white/70">Date: {voucher.date}</p>

                        {isReturning && voucher.returnComment && (
                          <p className="text-sm mt-2 bg-yellow-900/20 p-2 border border-yellow-800/30 rounded">
                            <span className="font-medium text-yellow-300">Return Reason:</span> {voucher.returnComment}
                          </p>
                        )}

                        {isRejected && rejectionComments[voucher.id] && (
                          <div className="mt-2 p-2 bg-red-900/20 border border-red-900/30 rounded-md">
                            <p className="text-xs text-red-300 font-medium">REJECTION REASON:</p>
                            <p className="text-sm text-white/90">{rejectionComments[voucher.id]}</p>
                          </div>
                        )}
                      </div>
                      <div>
                        <p className="text-sm mb-1"><span className="font-medium">Description:</span> {voucher.description}</p>
                        <p className="text-sm font-semibold">Amount: {voucher.amount.toFixed(2)} {voucher.currency}</p>

                        <div className="flex justify-end mt-3 gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className={`${isRejected ? 'bg-red-500 text-white hover:bg-red-600' : ''} px-4`}
                            onClick={() => isRejected ? handleAcceptVoucher(voucher.id) : handleStartRejectVoucher(voucher.id)}
                            disabled={!isEditable || isRejectedVoucher} // Disable reject button for rejected vouchers
                            title={isRejectedVoucher ? "Cannot reject an already rejected voucher" : ""}
                          >
                            <X className="h-4 w-4 mr-2" />
                            {isRejected ? 'Undo Reject' : 'Reject'}
                          </Button>

                          <Button
                            variant="outline"
                            size="sm"
                            className={`${isAccepted ? 'bg-green-500 text-white hover:bg-green-600' : ''} px-4`}
                            onClick={() => handleAcceptVoucher(voucher.id)}
                            disabled={!isEditable}
                          >
                            <Check className="h-4 w-4 mr-2" />
                            {isAccepted ? 'Undo Accept' : 'Accept'}
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                );
                })
              )}
            </div>
          </div>

          <DialogFooter className="pt-4 mt-2 border-t border-white/10">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting || isLoading}
              className="h-9"
            >
              Cancel
            </Button>
            <Button
              onClick={handleCompleteBatchReceiving}
              disabled={
                isSubmitting ||
                isLoading ||
                acceptedVouchers.length + rejectedVouchers.length !== batchVouchers.length ||
                !isEditable ||
                batchVouchers.length === 0
              }
              className="h-9 bg-blue-600 hover:bg-blue-700"
            >
              {isLoading ? 'Loading...' : 'Complete Processing'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Rejection dialog */}
      <VoucherRejectionDialog
        isOpen={rejectionDialogOpen}
        onClose={() => {
          setRejectionDialogOpen(false);
          setCurrentVoucherId(null);
        }}
        onConfirm={handleConfirmReject}
      />
    </>
  );
}
