<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VMS Offline Functionality Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-step {
            margin: 10px 0;
            padding: 10px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
        .success { border-left-color: #28a745; background-color: #d4edda; }
        .warning { border-left-color: #ffc107; background-color: #fff3cd; }
        .error { border-left-color: #dc3545; background-color: #f8d7da; }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .online { background-color: #28a745; }
        .offline { background-color: #dc3545; }
        .pending { background-color: #ffc107; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .test-results {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 4px;
        }
        .iframe-container {
            width: 100%;
            height: 600px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            margin: 20px 0;
        }
        .instructions {
            background-color: #cce5ff;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🧪 VMS Offline Functionality Test Suite</h1>
    
    <div class="test-section">
        <h2>📋 Test Overview</h2>
        <p>This test suite verifies the offline functionality of the VMS system. Follow the steps below to test each offline feature.</p>
        
        <div class="instructions">
            <strong>🎯 Testing Goal:</strong> Verify that users can continue working when network is lost and that operations sync when connection is restored.
        </div>
    </div>

    <div class="test-section">
        <h2>🌐 Network Status Test</h2>
        <div id="network-status">
            <span class="status-indicator online" id="status-dot"></span>
            <span id="status-text">Checking network status...</span>
        </div>
        
        <div class="test-step">
            <strong>Step 1:</strong> Verify network detection
            <button onclick="checkNetworkStatus()">Check Network Status</button>
            <div id="network-result"></div>
        </div>
        
        <div class="test-step">
            <strong>Step 2:</strong> Test offline simulation
            <button onclick="simulateOffline()">Simulate Offline</button>
            <button onclick="simulateOnline()">Simulate Online</button>
            <div id="simulation-result"></div>
        </div>
    </div>

    <div class="test-section">
        <h2>📱 VMS Application Test</h2>
        <div class="instructions">
            <strong>Instructions:</strong> The VMS application will load below. Use it to test offline functionality.
        </div>
        
        <iframe id="vms-iframe" src="http://localhost:8080" class="iframe-container"></iframe>
        
        <div class="test-step">
            <strong>Step 3:</strong> Login and verify online status
            <ol>
                <li>Login as Finance user in the iframe above</li>
                <li>Look for the offline status icon in the header (WiFi icon)</li>
                <li>Verify it shows green/online status</li>
            </ol>
            <button onclick="markStep3Complete()">Mark Step 3 Complete</button>
            <div id="step3-result"></div>
        </div>
    </div>

    <div class="test-section">
        <h2>📴 Offline Functionality Tests</h2>
        
        <div class="test-step">
            <strong>Step 4:</strong> Test offline voucher creation
            <ol>
                <li>Open browser DevTools (F12)</li>
                <li>Go to Network tab</li>
                <li>Check "Offline" checkbox</li>
                <li>Try creating a voucher in the VMS iframe</li>
                <li>Look for "queued offline" toast notification</li>
                <li>Check offline status icon for pending operations</li>
            </ol>
            <button onclick="markStep4Complete()">Mark Step 4 Complete</button>
            <div id="step4-result"></div>
        </div>
        
        <div class="test-step">
            <strong>Step 5:</strong> Test offline batch operations
            <ol>
                <li>While still offline, select some vouchers</li>
                <li>Try clicking "Send to Audit"</li>
                <li>Look for "batch queued offline" notification</li>
                <li>Check offline status for multiple pending operations</li>
            </ol>
            <button onclick="markStep5Complete()">Mark Step 5 Complete</button>
            <div id="step5-result"></div>
        </div>
        
        <div class="test-step">
            <strong>Step 6:</strong> Test auto-sync on reconnection
            <ol>
                <li>Uncheck "Offline" in DevTools Network tab</li>
                <li>Watch for "Connection restored" notification</li>
                <li>Look for auto-sync messages</li>
                <li>Verify operations complete successfully</li>
                <li>Check that offline status returns to green</li>
            </ol>
            <button onclick="markStep6Complete()">Mark Step 6 Complete</button>
            <div id="step6-result"></div>
        </div>
    </div>

    <div class="test-section">
        <h2>🔍 Advanced Testing</h2>
        
        <div class="test-step">
            <strong>Step 7:</strong> Test localStorage persistence
            <ol>
                <li>Go offline again</li>
                <li>Create a voucher</li>
                <li>Open DevTools Console</li>
                <li>Type: <code>localStorage.getItem('voucher-management-system')</code></li>
                <li>Verify offline operations are stored</li>
            </ol>
            <button onclick="markStep7Complete()">Mark Step 7 Complete</button>
            <div id="step7-result"></div>
        </div>
        
        <div class="test-step">
            <strong>Step 8:</strong> Test batch receiving offline
            <ol>
                <li>If you have batches from audit, try processing them offline</li>
                <li>Accept/reject vouchers while offline</li>
                <li>Verify processing is queued</li>
                <li>Go online and verify processing completes</li>
            </ol>
            <button onclick="markStep8Complete()">Mark Step 8 Complete</button>
            <div id="step8-result"></div>
        </div>
    </div>

    <div class="test-section">
        <h2>📊 Test Results Summary</h2>
        <div id="test-summary">
            <div class="test-results">
                <h3>Test Progress</h3>
                <div id="progress-summary">
                    <div>Step 1 (Network Status): <span id="step1-status">❌ Not Started</span></div>
                    <div>Step 2 (Offline Simulation): <span id="step2-status">❌ Not Started</span></div>
                    <div>Step 3 (Login & Online Status): <span id="step3-status">❌ Not Started</span></div>
                    <div>Step 4 (Offline Voucher Creation): <span id="step4-status">❌ Not Started</span></div>
                    <div>Step 5 (Offline Batch Operations): <span id="step5-status">❌ Not Started</span></div>
                    <div>Step 6 (Auto-Sync): <span id="step6-status">❌ Not Started</span></div>
                    <div>Step 7 (localStorage): <span id="step7-status">❌ Not Started</span></div>
                    <div>Step 8 (Batch Receiving): <span id="step8-status">❌ Not Started</span></div>
                </div>
                
                <div style="margin-top: 20px;">
                    <button onclick="generateTestReport()">Generate Test Report</button>
                    <div id="final-report"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let testResults = {
            step1: false, step2: false, step3: false, step4: false,
            step5: false, step6: false, step7: false, step8: false
        };

        function updateNetworkStatus() {
            const statusDot = document.getElementById('status-dot');
            const statusText = document.getElementById('status-text');
            
            if (navigator.onLine) {
                statusDot.className = 'status-indicator online';
                statusText.textContent = 'Online - Connected to network';
            } else {
                statusDot.className = 'status-indicator offline';
                statusText.textContent = 'Offline - No network connection';
            }
        }

        function checkNetworkStatus() {
            updateNetworkStatus();
            document.getElementById('network-result').innerHTML = 
                `<div class="success">✅ Network status checked: ${navigator.onLine ? 'Online' : 'Offline'}</div>`;
            testResults.step1 = true;
            updateProgressSummary();
        }

        function simulateOffline() {
            document.getElementById('simulation-result').innerHTML = 
                `<div class="warning">⚠️ Use DevTools Network tab to simulate offline. This button is for demonstration.</div>`;
            testResults.step2 = true;
            updateProgressSummary();
        }

        function simulateOnline() {
            document.getElementById('simulation-result').innerHTML = 
                `<div class="success">✅ Simulation controls tested. Use DevTools for actual offline testing.</div>`;
        }

        function markStep3Complete() {
            document.getElementById('step3-result').innerHTML = 
                `<div class="success">✅ Step 3 marked complete - Login and online status verified</div>`;
            testResults.step3 = true;
            updateProgressSummary();
        }

        function markStep4Complete() {
            document.getElementById('step4-result').innerHTML = 
                `<div class="success">✅ Step 4 marked complete - Offline voucher creation tested</div>`;
            testResults.step4 = true;
            updateProgressSummary();
        }

        function markStep5Complete() {
            document.getElementById('step5-result').innerHTML = 
                `<div class="success">✅ Step 5 marked complete - Offline batch operations tested</div>`;
            testResults.step5 = true;
            updateProgressSummary();
        }

        function markStep6Complete() {
            document.getElementById('step6-result').innerHTML = 
                `<div class="success">✅ Step 6 marked complete - Auto-sync functionality verified</div>`;
            testResults.step6 = true;
            updateProgressSummary();
        }

        function markStep7Complete() {
            document.getElementById('step7-result').innerHTML = 
                `<div class="success">✅ Step 7 marked complete - localStorage persistence verified</div>`;
            testResults.step7 = true;
            updateProgressSummary();
        }

        function markStep8Complete() {
            document.getElementById('step8-result').innerHTML = 
                `<div class="success">✅ Step 8 marked complete - Batch receiving offline tested</div>`;
            testResults.step8 = true;
            updateProgressSummary();
        }

        function updateProgressSummary() {
            const steps = ['step1', 'step2', 'step3', 'step4', 'step5', 'step6', 'step7', 'step8'];
            steps.forEach((step, index) => {
                const statusElement = document.getElementById(`${step}-status`);
                if (testResults[step]) {
                    statusElement.textContent = '✅ Complete';
                    statusElement.style.color = '#28a745';
                } else {
                    statusElement.textContent = '❌ Not Started';
                    statusElement.style.color = '#dc3545';
                }
            });
        }

        function generateTestReport() {
            const completedSteps = Object.values(testResults).filter(Boolean).length;
            const totalSteps = Object.keys(testResults).length;
            const completionRate = Math.round((completedSteps / totalSteps) * 100);
            
            const report = `
                <div class="test-results" style="margin-top: 20px;">
                    <h3>🎯 Final Test Report</h3>
                    <p><strong>Completion Rate:</strong> ${completedSteps}/${totalSteps} steps (${completionRate}%)</p>
                    <p><strong>Test Status:</strong> ${completionRate === 100 ? '✅ All tests completed' : '⚠️ Some tests pending'}</p>
                    <p><strong>Offline Functionality:</strong> ${completedSteps >= 6 ? '✅ Core features verified' : '❌ Core features need testing'}</p>
                    <p><strong>Recommendation:</strong> ${completionRate >= 75 ? 'Offline implementation ready for production' : 'Complete remaining tests before deployment'}</p>
                    <p><strong>Generated:</strong> ${new Date().toLocaleString()}</p>
                </div>
            `;
            
            document.getElementById('final-report').innerHTML = report;
        }

        // Initialize
        window.addEventListener('load', () => {
            updateNetworkStatus();
            updateProgressSummary();
        });

        // Listen for network changes
        window.addEventListener('online', updateNetworkStatus);
        window.addEventListener('offline', updateNetworkStatus);
    </script>
</body>
</html>
