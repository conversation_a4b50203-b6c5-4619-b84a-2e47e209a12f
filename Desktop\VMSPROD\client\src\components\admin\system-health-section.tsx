import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Server, 
  Database, 
  Users, 
  AlertTriangle, 
  CheckCircle,
  Clock,
  HardDrive,
  Cpu,
  RefreshCw,
  Wifi,
  Activity
} from 'lucide-react';

interface SystemHealth {
  database: {
    connections: number;
    maxConnections: number;
    sizeMB: number;
  };
  system: {
    activeUsers: number;
    recentErrors: number;
    uptime: number;
    memoryUsage: {
      rss: number;
      heapTotal: number;
      heapUsed: number;
      external: number;
    };
  };
  timestamp: string;
}

interface ActiveSession {
  user_id: string;
  user_name: string;
  department: string;
  session_start: string;
  last_activity: string;
  client_ip: string;
  is_active: boolean;
  minutes_idle: number;
}

export function SystemHealthSection() {
  const [health, setHealth] = useState<SystemHealth | null>(null);
  const [activeSessions, setActiveSessions] = useState<ActiveSession[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasAccess, setHasAccess] = useState(true);

  useEffect(() => {
    loadSystemHealth();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(loadSystemHealth, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadSystemHealth = async () => {
    setLoading(true);
    try {
      const [healthResponse, sessionsResponse] = await Promise.all([
        fetch('/api/audit-trail/system-health', { credentials: 'include' }),
        fetch('/api/audit-trail/active-sessions', { credentials: 'include' })
      ]);

      if (healthResponse.ok) {
        const healthData = await healthResponse.json();
        setHealth(healthData);
      } else if (healthResponse.status === 403) {
        setHasAccess(false);
      }

      if (sessionsResponse.ok) {
        const sessionsData = await sessionsResponse.json();
        setActiveSessions(sessionsData);
      } else if (sessionsResponse.status === 403) {
        setHasAccess(false);
      }
    } catch (error) {
      console.error('Error loading system health:', error);
      setHasAccess(false);
    } finally {
      setLoading(false);
    }
  };

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getHealthStatus = (value: number, max: number, threshold: number = 0.8) => {
    const percentage = value / max;
    if (percentage > threshold) {
      return { status: 'warning', color: 'text-yellow-600', bg: 'bg-yellow-100' };
    } else if (percentage > 0.9) {
      return { status: 'critical', color: 'text-red-600', bg: 'bg-red-100' };
    } else {
      return { status: 'healthy', color: 'text-green-600', bg: 'bg-green-100' };
    }
  };

  const getIdleStatus = (minutes: number) => {
    if (minutes > 30) {
      return { status: 'idle', color: 'text-gray-600', bg: 'bg-gray-100' };
    } else if (minutes > 10) {
      return { status: 'away', color: 'text-yellow-600', bg: 'bg-yellow-100' };
    } else {
      return { status: 'active', color: 'text-green-600', bg: 'bg-green-100' };
    }
  };

  // Show access denied message for non-admin users
  if (!hasAccess) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-2">
          <Server className="h-6 w-6 text-blue-600" />
          <h2 className="text-2xl font-bold">System Health</h2>
        </div>

        <div className="text-center py-12">
          <Server className="h-16 w-16 mx-auto mb-4 text-gray-400" />
          <h3 className="text-xl font-semibold mb-2">Admin Access Required</h3>
          <p className="text-gray-600 mb-4">
            System health monitoring is only available to system administrators.
          </p>
          <p className="text-sm text-gray-500">
            Contact your system administrator for access to system health data.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Server className="h-6 w-6 text-blue-600" />
          <h2 className="text-2xl font-bold">System Health</h2>
        </div>
        <Button onClick={loadSystemHealth} variant="outline" size="sm" disabled={loading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {health && (
        <>
          {/* System Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Database Connections */}
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">DB Connections</p>
                    <p className="text-2xl font-bold">
                      {health.database.connections}/{health.database.maxConnections}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {((health.database.connections / health.database.maxConnections) * 100).toFixed(1)}% used
                    </p>
                  </div>
                  <Database className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            {/* Active Users */}
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Active Users</p>
                    <p className="text-2xl font-bold">{health.system.activeUsers}</p>
                    <p className="text-xs text-muted-foreground">
                      {activeSessions.length} total sessions
                    </p>
                  </div>
                  <Users className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            {/* System Uptime */}
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">System Uptime</p>
                    <p className="text-2xl font-bold">{formatUptime(health.system.uptime)}</p>
                    <p className="text-xs text-muted-foreground">
                      Since last restart
                    </p>
                  </div>
                  <Clock className="h-8 w-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>

            {/* Recent Errors */}
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Recent Errors</p>
                    <p className={`text-2xl font-bold ${health.system.recentErrors > 0 ? 'text-red-600' : 'text-green-600'}`}>
                      {health.system.recentErrors}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Last hour
                    </p>
                  </div>
                  {health.system.recentErrors > 0 ? (
                    <AlertTriangle className="h-8 w-8 text-red-600" />
                  ) : (
                    <CheckCircle className="h-8 w-8 text-green-600" />
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Detailed Metrics */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Memory Usage */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Cpu className="mr-2 h-5 w-5" />
                  Memory Usage
                </CardTitle>
                <CardDescription>Server memory consumption</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Heap Used</span>
                    <span className="text-sm">{formatBytes(health.system.memoryUsage.heapUsed)}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ 
                        width: `${(health.system.memoryUsage.heapUsed / health.system.memoryUsage.heapTotal) * 100}%` 
                      }}
                    ></div>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Heap Total</span>
                    <span className="text-sm">{formatBytes(health.system.memoryUsage.heapTotal)}</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">RSS</span>
                    <span className="text-sm">{formatBytes(health.system.memoryUsage.rss)}</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">External</span>
                    <span className="text-sm">{formatBytes(health.system.memoryUsage.external)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Database Info */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <HardDrive className="mr-2 h-5 w-5" />
                  Database Information
                </CardTitle>
                <CardDescription>Database metrics and status</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Database Size</span>
                    <span className="text-sm font-bold">{health.database.sizeMB.toFixed(2)} MB</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Active Connections</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm">{health.database.connections}</span>
                      <Badge className={getHealthStatus(health.database.connections, health.database.maxConnections).bg}>
                        {getHealthStatus(health.database.connections, health.database.maxConnections).status}
                      </Badge>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Max Connections</span>
                    <span className="text-sm">{health.database.maxConnections}</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Connection Usage</span>
                    <span className="text-sm">
                      {((health.database.connections / health.database.maxConnections) * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Active Sessions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Wifi className="mr-2 h-5 w-5" />
                Active User Sessions
              </CardTitle>
              <CardDescription>Currently logged in users and their activity status</CardDescription>
            </CardHeader>
            <CardContent>
              {activeSessions.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No active sessions</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {activeSessions.map((session) => {
                    const idleStatus = getIdleStatus(session.minutes_idle);
                    return (
                      <div key={session.user_id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className={`w-3 h-3 rounded-full ${idleStatus.bg}`}></div>
                          <div>
                            <p className="font-medium">{session.user_name}</p>
                            <p className="text-sm text-gray-500">{session.department}</p>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <div className="text-right">
                            <p>Last Activity</p>
                            <p className="font-medium">
                              {session.minutes_idle === 0 ? 'Now' : `${session.minutes_idle}m ago`}
                            </p>
                          </div>
                          
                          <div className="text-right">
                            <p>IP Address</p>
                            <p className="font-medium">{session.client_ip}</p>
                          </div>
                          
                          <Badge className={idleStatus.bg}>
                            {idleStatus.status}
                          </Badge>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>

          {/* System Status Summary */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="text-sm font-medium">System Status: Healthy</span>
                </div>
                <div className="text-sm text-gray-500">
                  Last updated: {new Date(health.timestamp).toLocaleString()}
                </div>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
