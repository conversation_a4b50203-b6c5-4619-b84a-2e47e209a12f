import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Bell, 
  AlertTriangle, 
  Info, 
  CheckCircle, 
  X,
  RefreshCw,
  Settings,
  Users,
  Database,
  Server
} from 'lucide-react';

interface SystemNotification {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  category: 'system' | 'user' | 'security' | 'backup';
  priority: 'low' | 'medium' | 'high' | 'critical';
}

export function NotificationCenter() {
  const [notifications, setNotifications] = useState<SystemNotification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [filter, setFilter] = useState<'all' | 'unread' | 'critical'>('all');

  useEffect(() => {
    loadNotifications();
    
    // Auto-refresh every 60 seconds
    const interval = setInterval(loadNotifications, 60000);
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    const unread = notifications.filter(n => !n.read).length;
    setUnreadCount(unread);
  }, [notifications]);

  const loadNotifications = async () => {
    try {
      // Try to load real system notifications
      const [systemInfoResponse, recentActivitiesResponse] = await Promise.all([
        fetch('/api/system-info', { credentials: 'include' }).catch(() => null),
        fetch('/api/audit-trail/recent?limit=5', { credentials: 'include' }).catch(() => null)
      ]);

      const realNotifications: SystemNotification[] = [];

      // Generate notifications based on real system data
      if (systemInfoResponse?.ok) {
        const systemInfo = await systemInfoResponse.json();

        // Active users notification
        if (systemInfo.activeUsers > 0) {
          realNotifications.push({
            id: 'active-users',
            type: 'info',
            title: 'Users Currently Online',
            message: `${systemInfo.activeUsers} users are currently active in the system.`,
            timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
            read: true,
            category: 'user',
            priority: 'low'
          });
        }

        // System uptime notification
        const uptimeHours = Math.floor(systemInfo.uptime / 3600);
        if (uptimeHours > 24) {
          realNotifications.push({
            id: 'system-uptime',
            type: 'success',
            title: 'System Running Smoothly',
            message: `System has been running for ${uptimeHours} hours without issues.`,
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            read: true,
            category: 'system',
            priority: 'low'
          });
        }
      }

      // Add recent activity notifications
      if (recentActivitiesResponse?.ok) {
        const activities = await recentActivitiesResponse.json();
        if (activities.length > 0) {
          realNotifications.push({
            id: 'recent-activity',
            type: 'info',
            title: 'Recent System Activity',
            message: `${activities.length} recent activities recorded in the audit trail.`,
            timestamp: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
            read: false,
            category: 'system',
            priority: 'low'
          });
        }
      }

      // Add system status notification
      realNotifications.push({
        id: 'system-status',
        type: 'success',
        title: 'System Status: Healthy',
        message: 'All system components are operating normally.',
        timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
        read: true,
        category: 'system',
        priority: 'low'
      });

      // If we have real notifications, use them, otherwise fall back to mock data
      if (realNotifications.length > 1) {
        setNotifications(realNotifications);
        return;
      }
    } catch (error) {
      console.warn('Could not load real notifications, using mock data');
    }

    // Fallback mock notifications for demo purposes
    const mockNotifications: SystemNotification[] = [
      {
        id: '1',
        type: 'info',
        title: 'Daily Backup Completed',
        message: 'System backup for 2025-07-12 completed successfully. 1.2GB backed up.',
        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        read: false,
        category: 'backup',
        priority: 'low'
      },
      {
        id: '2',
        type: 'warning',
        title: 'High Database Connection Usage',
        message: 'Database connections at 85% capacity. Consider monitoring active sessions.',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        read: false,
        category: 'system',
        priority: 'medium'
      },
      {
        id: '3',
        type: 'success',
        title: 'User Registration Approved',
        message: 'New user "JOHN DOE" has been approved and activated for FINANCE department.',
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
        read: true,
        category: 'user',
        priority: 'low'
      },
      {
        id: '4',
        type: 'error',
        title: 'Failed Login Attempts',
        message: '5 failed login attempts detected from IP ************* in the last hour.',
        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        read: false,
        category: 'security',
        priority: 'high'
      },
      {
        id: '5',
        type: 'info',
        title: 'System Maintenance Scheduled',
        message: 'Routine system maintenance scheduled for this weekend. No downtime expected.',
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        read: true,
        category: 'system',
        priority: 'medium'
      }
    ];

    setNotifications(mockNotifications);
  };

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(n => ({ ...n, read: true }))
    );
  };

  const dismissNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'error': return <AlertTriangle className="h-5 w-5 text-red-600" />;
      case 'warning': return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      case 'success': return <CheckCircle className="h-5 w-5 text-green-600" />;
      default: return <Info className="h-5 w-5 text-blue-600" />;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'system': return <Server className="h-4 w-4" />;
      case 'user': return <Users className="h-4 w-4" />;
      case 'security': return <AlertTriangle className="h-4 w-4" />;
      case 'backup': return <Database className="h-4 w-4" />;
      default: return <Bell className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default: return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  };

  const filteredNotifications = notifications.filter(notification => {
    switch (filter) {
      case 'unread': return !notification.read;
      case 'critical': return notification.priority === 'critical' || notification.priority === 'high';
      default: return true;
    }
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="relative">
            <Bell className="h-6 w-6 text-blue-600" />
            {unreadCount > 0 && (
              <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                {unreadCount > 9 ? '9+' : unreadCount}
              </span>
            )}
          </div>
          <h2 className="text-2xl font-bold">Notification Center</h2>
        </div>
        <div className="flex items-center space-x-2">
          {unreadCount > 0 && (
            <Button onClick={markAllAsRead} variant="outline" size="sm">
              Mark All Read
            </Button>
          )}
          <Button onClick={loadNotifications} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="cursor-pointer hover:bg-gray-50" onClick={() => setFilter('all')}>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total</p>
                <p className="text-2xl font-bold">{notifications.length}</p>
              </div>
              <Bell className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:bg-gray-50" onClick={() => setFilter('unread')}>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Unread</p>
                <p className="text-2xl font-bold text-red-600">{unreadCount}</p>
              </div>
              <div className="relative">
                <Bell className="h-8 w-8 text-red-600" />
                {unreadCount > 0 && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:bg-gray-50" onClick={() => setFilter('critical')}>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Critical</p>
                <p className="text-2xl font-bold text-orange-600">
                  {notifications.filter(n => n.priority === 'critical' || n.priority === 'high').length}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">System Status</p>
                <p className="text-lg font-bold text-green-600">Healthy</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filter Buttons */}
      <div className="flex items-center space-x-2">
        <Button 
          variant={filter === 'all' ? 'default' : 'outline'} 
          size="sm"
          onClick={() => setFilter('all')}
        >
          All ({notifications.length})
        </Button>
        <Button 
          variant={filter === 'unread' ? 'default' : 'outline'} 
          size="sm"
          onClick={() => setFilter('unread')}
        >
          Unread ({unreadCount})
        </Button>
        <Button 
          variant={filter === 'critical' ? 'default' : 'outline'} 
          size="sm"
          onClick={() => setFilter('critical')}
        >
          Critical ({notifications.filter(n => n.priority === 'critical' || n.priority === 'high').length})
        </Button>
      </div>

      {/* Notifications List */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Notifications</CardTitle>
          <CardDescription>
            {filter === 'all' && `Showing all ${filteredNotifications.length} notifications`}
            {filter === 'unread' && `Showing ${filteredNotifications.length} unread notifications`}
            {filter === 'critical' && `Showing ${filteredNotifications.length} critical notifications`}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredNotifications.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Bell className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No notifications to display</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredNotifications.map((notification) => (
                <div 
                  key={notification.id} 
                  className={`border rounded-lg p-4 ${!notification.read ? 'bg-blue-50 border-blue-200' : 'bg-white'}`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 flex-1">
                      <div className="mt-1">
                        {getNotificationIcon(notification.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className="font-medium text-gray-900">{notification.title}</h4>
                          {!notification.read && (
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{notification.message}</p>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className="text-xs">
                            <div className="flex items-center space-x-1">
                              {getCategoryIcon(notification.category)}
                              <span>{notification.category}</span>
                            </div>
                          </Badge>
                          <Badge className={`text-xs ${getPriorityColor(notification.priority)}`}>
                            {notification.priority}
                          </Badge>
                          <span className="text-xs text-gray-500">
                            {formatTimestamp(notification.timestamp)}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      {!notification.read && (
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => markAsRead(notification.id)}
                        >
                          Mark Read
                        </Button>
                      )}
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => dismissNotification(notification.id)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
