import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  FileText, 
  Clock,
  Building,
  Activity,
  RefreshCw
} from 'lucide-react';

interface AnalyticsData {
  departmentActivity: Array<{
    department: string;
    activity_count: number;
    unique_users: number;
  }>;
  hourlyActivity: Array<{
    hour: number;
    activity_count: number;
  }>;
  topActions: Array<{
    action: string;
    count: number;
  }>;
  voucherMetrics: {
    created: number;
    dispatched: number;
    certified: number;
  };
  timeframe: string;
  generatedAt: string;
}

export function AnalyticsSection() {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(false);
  const [timeframe, setTimeframe] = useState('week');
  const [hasAccess, setHasAccess] = useState(true);

  useEffect(() => {
    loadAnalytics();
  }, [timeframe]);

  const loadAnalytics = async () => {
    setLoading(true);
    try {
      // Try to load real analytics data first
      const response = await fetch(`/api/basic-analytics?timeframe=${timeframe}`, {
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();

        // Transform the data to match our interface
        const transformedData: AnalyticsData = {
          departmentActivity: data.departmentActivity.map((dept: any) => ({
            department: dept.department,
            activity_count: dept.voucher_count,
            unique_users: 1 // We don't have this data in basic endpoint
          })),
          hourlyActivity: [], // Not available in basic endpoint
          topActions: [
            { action: 'CREATE_VOUCHER', count: data.voucherMetrics.total_vouchers },
            { action: 'DISPATCH_VOUCHER', count: data.voucherMetrics.dispatched },
            { action: 'CERTIFY_VOUCHER', count: data.voucherMetrics.certified }
          ].filter(action => action.count > 0),
          voucherMetrics: {
            created: data.voucherMetrics.total_vouchers,
            dispatched: data.voucherMetrics.dispatched,
            certified: data.voucherMetrics.certified
          },
          timeframe: data.timeframe,
          generatedAt: data.generatedAt
        };

        setAnalytics(transformedData);
      } else if (response.status === 403 || response.status === 401) {
        // Try admin endpoint as fallback
        const adminResponse = await fetch(`/api/audit-trail/analytics?timeframe=${timeframe}`, {
          credentials: 'include'
        });

        if (adminResponse.ok) {
          const adminData = await adminResponse.json();
          setAnalytics(adminData);
        } else {
          setHasAccess(false);
        }
      }
    } catch (error) {
      console.error('Error loading analytics:', error);
      setHasAccess(false);
    } finally {
      setLoading(false);
    }
  };

  const getActivityColor = (index: number) => {
    const colors = [
      'bg-blue-500',
      'bg-green-500', 
      'bg-purple-500',
      'bg-yellow-500',
      'bg-red-500',
      'bg-indigo-500',
      'bg-pink-500',
      'bg-teal-500'
    ];
    return colors[index % colors.length];
  };

  const formatActionName = (action: string) => {
    return action.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  // Show access denied message for non-admin users
  if (!hasAccess) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-2">
          <BarChart3 className="h-6 w-6 text-blue-600" />
          <h2 className="text-2xl font-bold">Analytics Dashboard</h2>
        </div>

        <div className="text-center py-12">
          <BarChart3 className="h-16 w-16 mx-auto mb-4 text-gray-400" />
          <h3 className="text-xl font-semibold mb-2">Admin Access Required</h3>
          <p className="text-gray-600 mb-4">
            Analytics dashboard is only available to system administrators.
          </p>
          <p className="text-sm text-gray-500">
            Contact your system administrator for access to system analytics.
          </p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <RefreshCw className="h-8 w-8 animate-spin mr-2" />
        <span>Loading analytics...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <BarChart3 className="h-6 w-6 text-blue-600" />
          <h2 className="text-2xl font-bold">Analytics Dashboard</h2>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={timeframe} onValueChange={setTimeframe}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="today">Today</SelectItem>
              <SelectItem value="week">This Week</SelectItem>
              <SelectItem value="month">This Month</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={loadAnalytics} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {analytics && (
        <>
          {/* Voucher Processing Metrics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="mr-2 h-5 w-5" />
                Voucher Processing Metrics
              </CardTitle>
              <CardDescription>
                Voucher workflow statistics for {timeframe}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600 mb-2">
                    {analytics.voucherMetrics.created}
                  </div>
                  <div className="text-sm text-gray-600">Vouchers Created</div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ 
                        width: `${Math.min(100, (analytics.voucherMetrics.created / Math.max(analytics.voucherMetrics.created, analytics.voucherMetrics.dispatched, analytics.voucherMetrics.certified)) * 100)}%` 
                      }}
                    ></div>
                  </div>
                </div>

                <div className="text-center">
                  <div className="text-3xl font-bold text-yellow-600 mb-2">
                    {analytics.voucherMetrics.dispatched}
                  </div>
                  <div className="text-sm text-gray-600">Vouchers Dispatched</div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div 
                      className="bg-yellow-600 h-2 rounded-full" 
                      style={{ 
                        width: `${Math.min(100, (analytics.voucherMetrics.dispatched / Math.max(analytics.voucherMetrics.created, analytics.voucherMetrics.dispatched, analytics.voucherMetrics.certified)) * 100)}%` 
                      }}
                    ></div>
                  </div>
                </div>

                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600 mb-2">
                    {analytics.voucherMetrics.certified}
                  </div>
                  <div className="text-sm text-gray-600">Vouchers Certified</div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div 
                      className="bg-green-600 h-2 rounded-full" 
                      style={{ 
                        width: `${Math.min(100, (analytics.voucherMetrics.certified / Math.max(analytics.voucherMetrics.created, analytics.voucherMetrics.dispatched, analytics.voucherMetrics.certified)) * 100)}%` 
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Department Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Building className="mr-2 h-5 w-5" />
                  Department Activity
                </CardTitle>
                <CardDescription>Activity breakdown by department</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analytics.departmentActivity.map((dept, index) => (
                    <div key={dept.department} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`w-4 h-4 rounded ${getActivityColor(index)}`}></div>
                        <div>
                          <div className="font-medium">{dept.department}</div>
                          <div className="text-sm text-gray-500">
                            {dept.unique_users} active users
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold">{dept.activity_count}</div>
                        <div className="text-sm text-gray-500">activities</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Top Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Activity className="mr-2 h-5 w-5" />
                  Top Actions
                </CardTitle>
                <CardDescription>Most frequent user actions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analytics.topActions.slice(0, 8).map((action, index) => (
                    <div key={action.action} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center text-xs font-bold">
                          {index + 1}
                        </div>
                        <div className="font-medium">
                          {formatActionName(action.action)}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div 
                            className={`${getActivityColor(index)} h-2 rounded-full`}
                            style={{ 
                              width: `${(action.count / analytics.topActions[0].count) * 100}%` 
                            }}
                          ></div>
                        </div>
                        <span className="font-bold text-sm w-8 text-right">{action.count}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Hourly Activity (Today only) */}
          {timeframe === 'today' && analytics.hourlyActivity.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Clock className="mr-2 h-5 w-5" />
                  Hourly Activity (Today)
                </CardTitle>
                <CardDescription>Activity distribution throughout the day</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-end space-x-1 h-32">
                  {Array.from({ length: 24 }, (_, hour) => {
                    const activity = analytics.hourlyActivity.find(a => a.hour === hour);
                    const count = activity?.activity_count || 0;
                    const maxCount = Math.max(...analytics.hourlyActivity.map(a => a.activity_count));
                    const height = maxCount > 0 ? (count / maxCount) * 100 : 0;
                    
                    return (
                      <div key={hour} className="flex-1 flex flex-col items-center">
                        <div 
                          className="w-full bg-blue-500 rounded-t"
                          style={{ height: `${height}%` }}
                          title={`${hour}:00 - ${count} activities`}
                        ></div>
                        <div className="text-xs mt-1 text-gray-500">
                          {hour.toString().padStart(2, '0')}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Summary */}
          <Card>
            <CardContent className="p-4">
              <div className="text-center text-sm text-gray-500">
                Analytics generated at {new Date(analytics.generatedAt).toLocaleString()} • 
                Timeframe: {analytics.timeframe}
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
