import express from 'express';
import { AuditService } from '../services/audit-service.js';
import { authenticate, authorize } from '../middleware/auth.js';
import { logger } from '../utils/logger.js';

const auditTrailRouter = express.Router();

// Apply authentication and admin authorization
auditTrailRouter.use(authenticate);
auditTrailRouter.use(authorize(['ADMIN']));

// Get audit logs with filtering
auditTrailRouter.get('/logs', async (req, res) => {
  try {
    const filters = {
      startDate: req.query.startDate as string,
      endDate: req.query.endDate as string,
      userId: req.query.userId as string,
      department: req.query.department as string,
      action: req.query.action as string,
      severity: req.query.severity as string,
      limit: parseInt(req.query.limit as string) || 100,
      offset: parseInt(req.query.offset as string) || 0
    };

    const logs = await AuditService.getAuditLogs(filters);
    res.json(logs);

  } catch (error) {
    logger.error('Get audit logs error:', error);
    res.status(500).json({ error: 'Failed to get audit logs' });
  }
});

// Get audit statistics
auditTrailRouter.get('/stats', async (req, res) => {
  try {
    const timeframe = req.query.timeframe as 'today' | 'week' | 'month' || 'today';
    const stats = await AuditService.getAuditStats(timeframe);
    res.json(stats);

  } catch (error) {
    logger.error('Get audit stats error:', error);
    res.status(500).json({ error: 'Failed to get audit statistics' });
  }
});

// Get recent activities for live feed
auditTrailRouter.get('/recent', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit as string) || 20;
    const activities = await AuditService.getRecentActivities(limit);
    res.json(activities);

  } catch (error) {
    logger.error('Get recent activities error:', error);
    res.status(500).json({ error: 'Failed to get recent activities' });
  }
});

// Get active sessions for monitoring
auditTrailRouter.get('/active-sessions', async (req, res) => {
  try {
    const { query } = await import('../database/db.js');
    
    const sessions = await query(`
      SELECT 
        user_id,
        user_name,
        department,
        session_start,
        last_activity,
        client_ip,
        is_active,
        TIMESTAMPDIFF(MINUTE, last_activity, NOW()) as minutes_idle
      FROM active_sessions 
      WHERE is_active = TRUE 
      ORDER BY last_activity DESC
    `) as any[];

    res.json(sessions);

  } catch (error) {
    logger.error('Get active sessions error:', error);
    res.status(500).json({ error: 'Failed to get active sessions' });
  }
});

// Get system health metrics
auditTrailRouter.get('/system-health', async (req, res) => {
  try {
    const { query } = await import('../database/db.js');
    
    // Get database connection count
    const connections = await query('SHOW STATUS LIKE "Threads_connected"') as any[];
    const maxConnections = await query('SHOW VARIABLES LIKE "max_connections"') as any[];
    
    // Get database size
    const dbSize = await query(`
      SELECT 
        ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
      FROM information_schema.tables 
      WHERE table_schema = DATABASE()
    `) as any[];

    // Get recent error count
    const recentErrors = await query(`
      SELECT COUNT(*) as error_count
      FROM audit_logs 
      WHERE severity = 'ERROR' 
      AND timestamp >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
    `) as any[];

    // Get active user count
    const activeUsers = await query(`
      SELECT COUNT(DISTINCT user_id) as active_count
      FROM active_sessions 
      WHERE is_active = TRUE 
      AND last_activity >= DATE_SUB(NOW(), INTERVAL 30 MINUTE)
    `) as any[];

    const health = {
      database: {
        connections: parseInt(connections[0]?.Value || '0'),
        maxConnections: parseInt(maxConnections[0]?.Value || '0'),
        sizeMB: parseFloat(dbSize[0]?.size_mb || '0')
      },
      system: {
        activeUsers: parseInt(activeUsers[0]?.active_count || '0'),
        recentErrors: parseInt(recentErrors[0]?.error_count || '0'),
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage()
      },
      timestamp: new Date().toISOString()
    };

    res.json(health);

  } catch (error) {
    logger.error('Get system health error:', error);
    res.status(500).json({ error: 'Failed to get system health' });
  }
});

// Get analytics data
auditTrailRouter.get('/analytics', async (req, res) => {
  try {
    const { query } = await import('../database/db.js');
    const timeframe = req.query.timeframe as string || 'week';
    
    let dateCondition = '';
    switch (timeframe) {
      case 'today':
        dateCondition = 'DATE(timestamp) = CURDATE()';
        break;
      case 'week':
        dateCondition = 'timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
        break;
      case 'month':
        dateCondition = 'timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
        break;
      default:
        dateCondition = 'timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
    }

    // Activity by department
    const departmentActivity = await query(`
      SELECT 
        department,
        COUNT(*) as activity_count,
        COUNT(DISTINCT user_id) as unique_users
      FROM audit_logs 
      WHERE ${dateCondition}
      GROUP BY department
      ORDER BY activity_count DESC
    `) as any[];

    // Activity by hour (for today)
    const hourlyActivity = await query(`
      SELECT 
        HOUR(timestamp) as hour,
        COUNT(*) as activity_count
      FROM audit_logs 
      WHERE DATE(timestamp) = CURDATE()
      GROUP BY HOUR(timestamp)
      ORDER BY hour
    `) as any[];

    // Top actions
    const topActions = await query(`
      SELECT 
        action,
        COUNT(*) as count
      FROM audit_logs 
      WHERE ${dateCondition}
      GROUP BY action
      ORDER BY count DESC
      LIMIT 10
    `) as any[];

    // Voucher processing metrics
    const voucherMetrics = await query(`
      SELECT 
        COUNT(CASE WHEN action = 'CREATE_VOUCHER' THEN 1 END) as created,
        COUNT(CASE WHEN action = 'DISPATCH_VOUCHERS' THEN 1 END) as dispatched,
        COUNT(CASE WHEN action = 'CERTIFY_VOUCHER' THEN 1 END) as certified
      FROM audit_logs 
      WHERE ${dateCondition}
    `) as any[];

    const analytics = {
      departmentActivity,
      hourlyActivity,
      topActions,
      voucherMetrics: voucherMetrics[0] || { created: 0, dispatched: 0, certified: 0 },
      timeframe,
      generatedAt: new Date().toISOString()
    };

    res.json(analytics);

  } catch (error) {
    logger.error('Get analytics error:', error);
    res.status(500).json({ error: 'Failed to get analytics data' });
  }
});

export { auditTrailRouter };
