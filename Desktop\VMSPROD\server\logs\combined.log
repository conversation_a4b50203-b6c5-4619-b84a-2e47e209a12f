{"level":"info","message":"🚀 Starting VMS Server - Production Grade Architecture...","service":"vms-server","timestamp":"2025-07-13 18:34:32"}
{"level":"info","message":"🎯 Using preferred port: 8080","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"🎯 Using dynamic port: 8080","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"✅ Single WebSocket system initialized - No duplicates","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"🎯 Duplicate broadcast prevention: Only socketHandlers active","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"🔄 Initializing database connection with enterprise retry logic...","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"Database connection pool initialized","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"Users already exist (3 users found)","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"✅ Database connected successfully - Production ready","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"🔍 Testing database connectivity...","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"📊 Database health monitoring DISABLED to prevent infinite loops","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"🎉 VMS Server - Production Grade Startup Complete!","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"🚀 Server Status: RUNNING (Port 8080)","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"🔌 WebSocket Status: ACTIVE (Enterprise Configuration)","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"💾 Database Status: CONNECTED","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"🌐 Environment: PRODUCTION ","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"📊 Process ID: 1228","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"📡 Service Endpoints:","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"   🌐 WebSocket: http://localhost:8080/socket.io/","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"   📊 Health Check: http://localhost:8080/health","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"   📈 Detailed Health: http://localhost:8080/health/detailed","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"   🔧 API Base: http://localhost:8080/api","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"✅ Enterprise tasks DISABLED to prevent infinite loops","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"✅ Production monitoring DISABLED for stability","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"🌍 Enterprise Network Access:","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"   🖥️  Server: http://************:8080","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"   💻 Client: http://************:3000","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"   🔌 WebSocket: http://************:8080/socket.io/","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"   🖥️  Server: http://*************:8080","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"   💻 Client: http://*************:3000","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"   🔌 WebSocket: http://*************:8080/socket.io/","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"📊 System Resources:","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"   💾 Memory: 61MB RSS","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"   🔄 Uptime: 2s","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"🎯 VMS Service Discovery started - Broadcasting on all network interfaces","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"📊 Service Info: {\n  \"serviceName\": \"VMS-Server\",\n  \"host\": \"************\",\n  \"port\": 8080,\n  \"version\": \"3.0.0\",\n  \"timestamp\": 1752431673343,\n  \"capabilities\": [\n    \"voucher-management\",\n    \"real-time-updates\",\n    \"multi-user\"\n  ]\n}","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"📡 Service Discovery: ACTIVE - Clients can auto-discover this server","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"🎯 VMS Server - Ready for Production Operations!","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"📡 Service discovery broadcast started on port 45454","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"🔍 Service discovery listener started on port 45455","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
