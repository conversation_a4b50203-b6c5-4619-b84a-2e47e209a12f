{"level":"info","message":"🚀 Starting VMS Server - Production Grade Architecture...","service":"vms-server","timestamp":"2025-07-13 18:34:32"}
{"level":"info","message":"🎯 Using preferred port: 8080","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"🎯 Using dynamic port: 8080","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"✅ Single WebSocket system initialized - No duplicates","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"🎯 Duplicate broadcast prevention: Only socketHandlers active","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"🔄 Initializing database connection with enterprise retry logic...","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"Database connection pool initialized","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"Users already exist (3 users found)","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"✅ Database connected successfully - Production ready","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"🔍 Testing database connectivity...","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"📊 Database health monitoring DISABLED to prevent infinite loops","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"🎉 VMS Server - Production Grade Startup Complete!","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"🚀 Server Status: RUNNING (Port 8080)","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"🔌 WebSocket Status: ACTIVE (Enterprise Configuration)","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"💾 Database Status: CONNECTED","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"🌐 Environment: PRODUCTION ","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"📊 Process ID: 1228","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"📡 Service Endpoints:","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"   🌐 WebSocket: http://localhost:8080/socket.io/","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"   📊 Health Check: http://localhost:8080/health","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"   📈 Detailed Health: http://localhost:8080/health/detailed","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"   🔧 API Base: http://localhost:8080/api","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"✅ Enterprise tasks DISABLED to prevent infinite loops","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"✅ Production monitoring DISABLED for stability","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"🌍 Enterprise Network Access:","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"   🖥️  Server: http://************:8080","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"   💻 Client: http://************:3000","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"   🔌 WebSocket: http://************:8080/socket.io/","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"   🖥️  Server: http://*************:8080","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"   💻 Client: http://*************:3000","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"   🔌 WebSocket: http://*************:8080/socket.io/","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"📊 System Resources:","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"   💾 Memory: 61MB RSS","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"   🔄 Uptime: 2s","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"🎯 VMS Service Discovery started - Broadcasting on all network interfaces","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"📊 Service Info: {\n  \"serviceName\": \"VMS-Server\",\n  \"host\": \"************\",\n  \"port\": 8080,\n  \"version\": \"3.0.0\",\n  \"timestamp\": 1752431673343,\n  \"capabilities\": [\n    \"voucher-management\",\n    \"real-time-updates\",\n    \"multi-user\"\n  ]\n}","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"📡 Service Discovery: ACTIVE - Clients can auto-discover this server","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"🎯 VMS Server - Ready for Production Operations!","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"📡 Service discovery broadcast started on port 45454","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"🔍 Service discovery listener started on port 45455","service":"vms-server","timestamp":"2025-07-13 18:34:33"}
{"level":"info","message":"🔍 Checking for year rollover...","service":"vms-server","timestamp":"2025-07-13 18:35:32"}
{"level":"info","message":"🗓️ Year rollover monitoring started - checking daily","service":"vms-server","timestamp":"2025-07-13 18:35:32"}
{"level":"info","message":"⏰ Using system time override: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-07-13 18:35:32"}
{"level":"info","message":"📅 Current date: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-07-13 18:35:32"}
{"level":"info","message":"📊 Calendar year: 2025","service":"vms-server","timestamp":"2025-07-13 18:35:32"}
{"level":"info","message":"📈 Calculated fiscal year: 2025","service":"vms-server","timestamp":"2025-07-13 18:35:32"}
{"level":"info","message":"⚙️ Configured fiscal year: 2025","service":"vms-server","timestamp":"2025-07-13 18:35:32"}
{"level":"info","message":"\u001b[0mGET / \u001b[32m200\u001b[0m 12.578 ms - 1201\u001b[0m","service":"vms-server","timestamp":"2025-07-13 18:36:21"}
{"level":"info","message":"\u001b[0mGET /assets/index-0-BGADIb-1752431677009.css \u001b[32m200\u001b[0m 7.712 ms - 89907\u001b[0m","service":"vms-server","timestamp":"2025-07-13 18:36:21"}
{"level":"info","message":"\u001b[0mGET /assets/index-CvoT5IUu-1752431677009.js \u001b[32m200\u001b[0m 9.079 ms - 1375058\u001b[0m","service":"vms-server","timestamp":"2025-07-13 18:36:21"}
{"ip":"::1","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-07-13T18:36:21.588Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 12.431 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-07-13 18:36:21"}
{"ip":"::1","level":"info","message":"API Request: GET /auth/users-by-department","service":"vms-server","timestamp":"2025-07-13T18:36:21.783Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"Fetched 3 users for login dropdown","service":"vms-server","timestamp":"2025-07-13 18:36:21"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department \u001b[32m200\u001b[0m 13.904 ms - 262\u001b[0m","service":"vms-server","timestamp":"2025-07-13 18:36:21"}
{"ip":"::1","level":"info","message":"API Request: POST /auth/login","service":"vms-server","timestamp":"2025-07-13T18:36:59.237Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"✅ Successful login: SAMUEL ASIEDU (AUDIT) - Role: USER","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'user_name' in 'field list'","service":"vms-server","sql":"INSERT INTO audit_logs (\n          id, timestamp, user_id, user_name, department, action, description, \n          resource_type, resource_id, details, ip_address, user_agent, severity\n        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","sqlMessage":"Unknown column 'user_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'user_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at query (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\database\\db.js:503:38)\n    at AuditService.logActivity (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\services\\audit-service.js:62:37)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\routes\\auth.js:54:13","timestamp":"2025-07-13 18:36:59"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Unknown column 'user_name' in 'field list'","service":"vms-server","sql":"INSERT INTO audit_logs (\n          id, timestamp, user_id, user_name, department, action, description, \n          resource_type, resource_id, details, ip_address, user_agent, severity\n        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","sqlMessage":"Unknown column 'user_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'user_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at query (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\database\\db.js:503:38)\n    at AuditService.logActivity (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\services\\audit-service.js:62:37)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\routes\\auth.js:54:13","timestamp":"2025-07-13 18:36:59"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"warn","message":"Unknown column 'user_name' in 'field list'","service":"vms-server","sql":"INSERT INTO audit_logs (\n          id, timestamp, user_id, user_name, department, action, description, \n          resource_type, resource_id, details, ip_address, user_agent, severity\n        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)","sqlMessage":"Unknown column 'user_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'user_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at query (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\database\\db.js:503:38)\n    at AuditService.logActivity (C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\services\\audit-service.js:62:37)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Desktop\\VMSPROD\\server\\dist\\routes\\auth.js:54:13","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"\u001b[0mPOST /api/auth/login \u001b[32m200\u001b[0m 172.466 ms - 263\u001b[0m","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 1.719 ms - 288\u001b[0m","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"ip":"::1","level":"info","message":"API Request: GET /vouchers?department=AUDIT&timestamp=1752431819336","service":"vms-server","timestamp":"2025-07-13T18:36:59.356Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=AUDIT&timestamp=1752431819336","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"GET /vouchers request at 2025-07-13T18:36:59.400Z by user SAMUEL ASIEDU (AUDIT), query department: AUDIT","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"Fetching vouchers for specified department: AUDIT","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"Found 2 vouchers for request from SAMUEL ASIEDU (AUDIT)","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"Sample vouchers: FINJUL0003 (38ef4e18-3c8e-43aa-b573-68c4891cad0d): AUDIT: PROCESSING, FINJUL0005 (797c097a-af59-439d-a715-f94fd03fd04f): AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=AUDIT&timestamp=1752431819336 \u001b[32m200\u001b[0m 65.398 ms - 4814\u001b[0m","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"ip":"::1","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-07-13T18:36:59.437Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"⏰ Using system time override: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[36m304\u001b[0m 24.776 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"ip":"::1","level":"info","message":"API Request: GET /years/current","service":"vms-server","timestamp":"2025-07-13T18:36:59.466Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🔐 LAN Authentication for: GET /current","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"\u001b[0mGET /api/years/current \u001b[36m304\u001b[0m 11.985 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"ip":"::1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-07-13T18:36:59.494Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"ip":"::1","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-07-13T18:36:59.496Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"Found 1 online users in AUDIT department","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 13.996 ms - 131\u001b[0m","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[36m304\u001b[0m 37.804 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"WebSocket connection from ::1 - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: SAMUEL ASIEDU (AUDIT)","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"🔌 WebSocket connection established: SAMUEL ASIEDU (f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d) from AUDIT [Auth: YES]","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"🚀 Setting up authenticated connection for SAMUEL ASIEDU (f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d) from AUDIT","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"User SAMUEL ASIEDU (f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d) joined room: department:AUDIT","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"User SAMUEL ASIEDU (f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d) joined room: admin-users","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"User SAMUEL ASIEDU (f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d) joined personal room: user:f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"Updated existing session with socket info for SAMUEL ASIEDU (f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d)","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"Total connected clients: 1, 1 in department AUDIT","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"Socket nsnMfuBC3bzHdcHdAAAB is in rooms: nsnMfuBC3bzHdcHdAAAB, department:AUDIT, admin-users, user:f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"ip":"::1","level":"info","message":"API Request: GET /vouchers?department=AUDIT&timestamp=1752431819611","service":"vms-server","timestamp":"2025-07-13T18:36:59.628Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=AUDIT&timestamp=1752431819611","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"ip":"::1","level":"info","message":"API Request: GET /users?_t=1752431819612","service":"vms-server","timestamp":"2025-07-13T18:36:59.630Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?_t=1752431819612","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"GET /vouchers request at 2025-07-13T18:36:59.641Z by user SAMUEL ASIEDU (AUDIT), query department: AUDIT","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"Fetching vouchers for specified department: AUDIT","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"Found 2 vouchers for request from SAMUEL ASIEDU (AUDIT)","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"Sample vouchers: FINJUL0003 (38ef4e18-3c8e-43aa-b573-68c4891cad0d): AUDIT: PROCESSING, FINJUL0005 (797c097a-af59-439d-a715-f94fd03fd04f): AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=AUDIT&timestamp=1752431819611 \u001b[32m200\u001b[0m 33.724 ms - 4814\u001b[0m","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"level":"info","message":"\u001b[0mGET /api/users?_t=1752431819612 \u001b[32m200\u001b[0m 32.715 ms - 641\u001b[0m","service":"vms-server","timestamp":"2025-07-13 18:36:59"}
{"ip":"::1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-07-13T18:37:30.013Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-07-13 18:37:30"}
{"level":"info","message":"Found 1 online users in AUDIT department","service":"vms-server","timestamp":"2025-07-13 18:37:30"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[36m304\u001b[0m 25.342 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-07-13 18:37:30"}
{"ip":"::1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-07-13T18:38:00.021Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-07-13 18:38:00"}
{"level":"info","message":"Found 1 online users in AUDIT department","service":"vms-server","timestamp":"2025-07-13 18:38:00"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[36m304\u001b[0m 21.913 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-07-13 18:38:00"}
{"ip":"::1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-07-13T18:38:30.003Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-07-13 18:38:30"}
{"level":"info","message":"Found 1 online users in AUDIT department","service":"vms-server","timestamp":"2025-07-13 18:38:30"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[36m304\u001b[0m 22.566 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-07-13 18:38:30"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 3.769 ms - 289\u001b[0m","service":"vms-server","timestamp":"2025-07-13 18:39:00"}
{"ip":"::1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-07-13T18:39:00.004Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-07-13 18:39:00"}
{"level":"info","message":"Found 1 online users in AUDIT department","service":"vms-server","timestamp":"2025-07-13 18:39:00"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[36m304\u001b[0m 25.677 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-07-13 18:39:00"}
{"ip":"::1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-07-13T18:39:30.001Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-07-13 18:39:30"}
{"level":"info","message":"Found 1 online users in AUDIT department","service":"vms-server","timestamp":"2025-07-13 18:39:30"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[36m304\u001b[0m 20.959 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-07-13 18:39:30"}
{"ip":"::1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-07-13T18:40:00.001Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-07-13 18:40:00"}
{"level":"info","message":"Found 1 online users in AUDIT department","service":"vms-server","timestamp":"2025-07-13 18:40:00"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[36m304\u001b[0m 19.353 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-07-13 18:40:00"}
{"ip":"::1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-07-13T18:40:36.996Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-07-13 18:40:36"}
{"level":"info","message":"Found 1 online users in AUDIT department","service":"vms-server","timestamp":"2025-07-13 18:40:37"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[36m304\u001b[0m 19.638 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-07-13 18:40:37"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 1.194 ms - 289\u001b[0m","service":"vms-server","timestamp":"2025-07-13 18:41:00"}
{"ip":"::1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-07-13T18:41:37.010Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-07-13 18:41:37"}
{"level":"info","message":"Found 1 online users in AUDIT department","service":"vms-server","timestamp":"2025-07-13 18:41:37"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[36m304\u001b[0m 21.416 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-07-13 18:41:37"}
