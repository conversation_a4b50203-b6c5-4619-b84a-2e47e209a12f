import { StateCreator } from 'zustand';
import { toast } from 'sonner';

export interface OfflineOperation {
  id: string;
  type: 'CREATE_VOUCHER' | 'UPDATE_VOUCHER' | 'SEND_TO_AUDIT' | 'RECEIVE_BATCH' | 'UPDATE_STATUS';
  data: any;
  timestamp: number;
  status: 'pending' | 'syncing' | 'synced' | 'failed';
  retryCount: number;
  maxRetries: number;
  error?: string;
}

export interface OfflineSlice {
  // State
  isOnline: boolean;
  offlineOperations: OfflineOperation[];
  isSyncing: boolean;
  lastSyncTime: number | null;

  // Actions
  setOnlineStatus: (isOnline: boolean) => void;
  addOfflineOperation: (operation: Omit<OfflineOperation, 'id' | 'timestamp' | 'status' | 'retryCount'>) => void;
  removeOfflineOperation: (id: string) => void;
  updateOperationStatus: (id: string, status: OfflineOperation['status'], error?: string) => void;
  syncOfflineOperations: () => Promise<void>;
  clearSyncedOperations: () => void;
  getOperationsByType: (type: OfflineOperation['type']) => OfflineOperation[];
}

export const createOfflineSlice: StateCreator<OfflineSlice> = (set, get) => ({
  // Initial state
  isOnline: navigator.onLine,
  offlineOperations: [],
  isSyncing: false,
  lastSyncTime: null,

  // Set online status
  setOnlineStatus: (isOnline: boolean) => {
    const wasOffline = !get().isOnline;
    set({ isOnline });
    
    // Auto-sync when coming back online
    if (isOnline && wasOffline) {
      console.log('🌐 Connection restored - starting auto-sync');
      toast.success('Connection restored - syncing offline changes...');
      setTimeout(() => {
        get().syncOfflineOperations();
      }, 1000); // Small delay to ensure connection is stable
    } else if (!isOnline) {
      console.log('📴 Connection lost - entering offline mode');
      toast.info('Connection lost - operations will be queued for sync');
    }
  },

  // Add operation to offline queue
  addOfflineOperation: (operation) => {
    const newOperation: OfflineOperation = {
      ...operation,
      id: `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      status: 'pending',
      retryCount: 0,
      maxRetries: operation.maxRetries || 3
    };

    set(state => ({
      offlineOperations: [...state.offlineOperations, newOperation]
    }));

    console.log(`📝 Queued offline operation: ${operation.type}`, newOperation);
    toast.info(`Operation queued offline: ${operation.type.replace('_', ' ').toLowerCase()}`);
  },

  // Remove operation from queue
  removeOfflineOperation: (id: string) => {
    set(state => ({
      offlineOperations: state.offlineOperations.filter(op => op.id !== id)
    }));
  },

  // Update operation status
  updateOperationStatus: (id: string, status: OfflineOperation['status'], error?: string) => {
    set(state => ({
      offlineOperations: state.offlineOperations.map(op =>
        op.id === id ? { ...op, status, error } : op
      )
    }));
  },

  // Sync all pending offline operations
  syncOfflineOperations: async () => {
    const { offlineOperations, isOnline } = get();
    
    if (!isOnline) {
      console.log('❌ Cannot sync - still offline');
      return;
    }

    const pendingOperations = offlineOperations.filter(op => 
      op.status === 'pending' || (op.status === 'failed' && op.retryCount < op.maxRetries)
    );

    if (pendingOperations.length === 0) {
      console.log('✅ No pending operations to sync');
      return;
    }

    set({ isSyncing: true });
    console.log(`🔄 Starting sync of ${pendingOperations.length} operations`);

    let successCount = 0;
    let failureCount = 0;

    for (const operation of pendingOperations) {
      try {
        get().updateOperationStatus(operation.id, 'syncing');
        
        // Execute the operation based on type
        await executeOfflineOperation(operation);
        
        get().updateOperationStatus(operation.id, 'synced');
        successCount++;
        
        console.log(`✅ Synced operation: ${operation.type}`);
        
      } catch (error) {
        console.error(`❌ Failed to sync operation ${operation.type}:`, error);
        
        const newRetryCount = operation.retryCount + 1;
        if (newRetryCount >= operation.maxRetries) {
          get().updateOperationStatus(operation.id, 'failed', error.message);
          failureCount++;
        } else {
          // Update retry count and set back to pending for next sync attempt
          set(state => ({
            offlineOperations: state.offlineOperations.map(op =>
              op.id === operation.id ? { ...op, retryCount: newRetryCount, status: 'pending' } : op
            )
          }));
        }
      }
    }

    set({ 
      isSyncing: false, 
      lastSyncTime: Date.now() 
    });

    // Show sync results
    if (successCount > 0) {
      toast.success(`✅ Synced ${successCount} operations successfully`);
    }
    if (failureCount > 0) {
      toast.error(`❌ ${failureCount} operations failed to sync`);
    }

    console.log(`🔄 Sync completed: ${successCount} success, ${failureCount} failed`);
  },

  // Clear synced operations
  clearSyncedOperations: () => {
    set(state => ({
      offlineOperations: state.offlineOperations.filter(op => op.status !== 'synced')
    }));
  },

  // Get operations by type
  getOperationsByType: (type: OfflineOperation['type']) => {
    return get().offlineOperations.filter(op => op.type === type);
  }
});

// Execute offline operation based on type
async function executeOfflineOperation(operation: OfflineOperation): Promise<void> {
  switch (operation.type) {
    case 'CREATE_VOUCHER':
      await fetch('/api/vouchers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(operation.data)
      });
      break;
      
    case 'UPDATE_VOUCHER':
      await fetch(`/api/vouchers/${operation.data.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(operation.data)
      });
      break;
      
    case 'SEND_TO_AUDIT':
      await fetch('/api/batches', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(operation.data)
      });
      break;
      
    case 'RECEIVE_BATCH':
      await fetch(`/api/batches/${operation.data.batchId}/receive`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(operation.data)
      });
      break;
      
    case 'UPDATE_STATUS':
      await fetch(`/api/vouchers/${operation.data.id}/status`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ status: operation.data.status })
      });
      break;
      
    default:
      throw new Error(`Unknown operation type: ${operation.type}`);
  }
}
