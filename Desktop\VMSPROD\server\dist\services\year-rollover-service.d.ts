export declare class YearRolloverService {
    private rolloverCheckInterval;
    private readonly CHECK_INTERVAL;
    private rolloverInProgress;
    private rolloverStatus;
    constructor();
    /**
     * Start monitoring for automatic year rollover
     */
    private startRolloverMonitoring;
    /**
     * Stop monitoring (for graceful shutdown)
     */
    stopRolloverMonitoring(): void;
    /**
     * Check if year rollover is needed based on fiscal year configuration
     */
    private checkForYearRollover;
    /**
     * Get system settings from database
     */
    private getSystemSettings;
    /**
     * Get current date (with system time override support)
     */
    private getCurrentDate;
    /**
     * Calculate fiscal year based on current date and fiscal year configuration
     * CONSERVATIVE APPROACH: Only rollover when we're clearly in the next fiscal year
     */
    private calculateFiscalYear;
    /**
     * Perform automatic year rollover with detailed status tracking
     */
    private performYearRollover;
    /**
     * Update rollover step status
     */
    private updateRolloverStep;
    /**
     * Create backup before year rollover
     */
    private createYearRolloverBackup;
    /**
     * Log year rollover event
     */
    private logYearRolloverEvent;
    /**
     * Notify administrators of successful rollover
     */
    private notifyAdministrators;
    /**
     * Notify administrators of rollover failure
     */
    private notifyRolloverFailure;
    /**
     * Manual year rollover trigger (for admin use)
     */
    triggerManualRollover(targetYear: number): Promise<boolean>;
    /**
     * Get rollover status and next rollover date
     */
    getRolloverStatus(): Promise<any>;
    /**
     * Calculate next rollover date
     */
    private calculateNextRolloverDate;
}
export declare const yearRolloverService: YearRolloverService;
