"use strict";
/**
 * This file defines the standard voucher status flow and transitions
 * to ensure consistency across the application.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.VALID_STATUS_TRANSITIONS = exports.VOUCHER_STATUSES = void 0;
exports.isValidStatusTransition = isValidStatusTransition;
exports.getFlagsForStatus = getFlagsForStatus;
exports.synchronizeVoucherFlags = synchronizeVoucherFlags;
exports.getNextAllowedStatuses = getNextAllowedStatuses;
exports.logStatusTransition = logStatusTransition;
const logger_js_1 = require("./logger.js");
// Define all possible voucher statuses
exports.VOUCHER_STATUSES = {
    // Department statuses
    PENDING: 'PENDING',
    PENDING_SUBMISSION: 'PENDING SUBMISSION',
    PENDING_RECEIPT: 'PENDING RECEIPT',
    VOUCHER_PROCESSING: 'VOUCHER PROCESSING',
    // Audit statuses
    AUDIT_PROCESSING: 'AUDIT: PROCESSING',
    // Final statuses
    VOUCHER_CERTIFIED: 'VOUCHER CERTIFIED',
    VOUCHER_REJECTED: 'VOUCHER REJECTED',
    REJECTED_PENDING_DISPATCH: 'REJECTED: PENDING DISPATCH',
    VOUCHER_RETURNED: 'VOUCHER RETURNED',
    // Other statuses
    VOUCHER_PENDING_RETURN: 'VOUCHER PENDING RETURN',
    // System statuses
    OFFSET_BY_AUDIT: 'OFFSET_BY_AUDIT',
};
// COMPREHENSIVE SOLUTION: Define valid status transitions with metadata
exports.VALID_STATUS_TRANSITIONS = {
    // PENDING can transition to PENDING_RECEIPT when sent to audit
    [exports.VOUCHER_STATUSES.PENDING]: {
        allowedTransitions: [exports.VOUCHER_STATUSES.PENDING_RECEIPT],
        requiredFlags: { sentToAudit: true },
        clearFlags: [],
        requiredRole: ['admin', 'USER'] // FIXED: USER role can send vouchers to audit
    },
    // PENDING_SUBMISSION can also transition to PENDING_RECEIPT (backward compatibility)
    [exports.VOUCHER_STATUSES.PENDING_SUBMISSION]: {
        allowedTransitions: [exports.VOUCHER_STATUSES.PENDING_RECEIPT],
        requiredFlags: { sentToAudit: true },
        clearFlags: [],
        requiredRole: ['admin', 'USER'] // FIXED: USER role can send vouchers to audit
    },
    [exports.VOUCHER_STATUSES.PENDING_RECEIPT]: {
        allowedTransitions: [exports.VOUCHER_STATUSES.AUDIT_PROCESSING],
        requiredFlags: {}, // CRITICAL FIX: Don't require receivedByAudit flag - it gets set during transition
        clearFlags: [],
        requiredRole: ['AUDIT', 'SYSTEM ADMIN']
    },
    [exports.VOUCHER_STATUSES.AUDIT_PROCESSING]: {
        allowedTransitions: [
            exports.VOUCHER_STATUSES.VOUCHER_CERTIFIED,
            exports.VOUCHER_STATUSES.VOUCHER_REJECTED,
            exports.VOUCHER_STATUSES.REJECTED_PENDING_DISPATCH,
            exports.VOUCHER_STATUSES.VOUCHER_PENDING_RETURN,
            exports.VOUCHER_STATUSES.VOUCHER_RETURNED
        ],
        requiredFlags: {},
        clearFlags: [],
        requiredRole: ['AUDIT', 'SYSTEM ADMIN']
    },
    [exports.VOUCHER_STATUSES.VOUCHER_PENDING_RETURN]: {
        allowedTransitions: [exports.VOUCHER_STATUSES.VOUCHER_RETURNED],
        requiredFlags: { pendingReturn: true },
        clearFlags: ['pendingReturn'],
        requiredRole: ['AUDIT', 'SYSTEM ADMIN']
    },
    [exports.VOUCHER_STATUSES.VOUCHER_CERTIFIED]: {
        allowedTransitions: [exports.VOUCHER_STATUSES.VOUCHER_PROCESSING],
        requiredFlags: { dispatched: true },
        clearFlags: [],
        requiredRole: ['admin', 'USER'] // FIXED: USER role can handle voucher processing
    },
    [exports.VOUCHER_STATUSES.VOUCHER_REJECTED]: {
        allowedTransitions: [exports.VOUCHER_STATUSES.VOUCHER_PROCESSING],
        requiredFlags: { dispatched: true },
        clearFlags: [],
        requiredRole: ['admin', 'USER'] // FIXED: USER role can handle voucher processing
    },
    [exports.VOUCHER_STATUSES.REJECTED_PENDING_DISPATCH]: {
        allowedTransitions: [exports.VOUCHER_STATUSES.VOUCHER_REJECTED],
        requiredFlags: {},
        clearFlags: [],
        requiredRole: ['AUDIT', 'SYSTEM ADMIN']
    },
    [exports.VOUCHER_STATUSES.VOUCHER_RETURNED]: {
        allowedTransitions: [exports.VOUCHER_STATUSES.VOUCHER_PROCESSING],
        requiredFlags: { dispatched: true },
        clearFlags: [],
        requiredRole: ['admin', 'USER'] // FIXED: USER role can handle voucher processing
    },
    [exports.VOUCHER_STATUSES.VOUCHER_PROCESSING]: {
        allowedTransitions: [
            exports.VOUCHER_STATUSES.VOUCHER_CERTIFIED,
            exports.VOUCHER_STATUSES.VOUCHER_REJECTED,
            exports.VOUCHER_STATUSES.VOUCHER_RETURNED,
            exports.VOUCHER_STATUSES.PENDING_SUBMISSION
        ],
        requiredFlags: {},
        clearFlags: ['sentToAudit', 'dispatched', 'certified', 'rejected', 'isReturned', 'pendingReturn', 'receivedByAudit'],
        requiredRole: ['admin', 'USER'] // FIXED: USER role can handle voucher processing
    }
};
// Validate if a status transition is allowed - DUAL ROLE SYSTEM FIX
function isValidStatusTransition(currentStatus, newStatus, userRole, flags, userDepartment) {
    const transition = exports.VALID_STATUS_TRANSITIONS[currentStatus];
    if (!transition) {
        return { isValid: false, reason: `Invalid current status: ${currentStatus}` };
    }
    if (!transition.allowedTransitions.includes(newStatus)) {
        return {
            isValid: false,
            reason: `Cannot transition from ${currentStatus} to ${newStatus}. Allowed transitions: ${transition.allowedTransitions.join(', ')}`
        };
    }
    // DUAL ROLE SYSTEM: Check admin role gate first
    if (userRole === 'VIEWER') {
        return {
            isValid: false,
            reason: `VIEWER role cannot perform any operations. Contact admin to change your role.`
        };
    }
    // DUAL ROLE SYSTEM: Check department-based permissions for specific transitions
    if (currentStatus === exports.VOUCHER_STATUSES.PENDING_RECEIPT && newStatus === exports.VOUCHER_STATUSES.AUDIT_PROCESSING) {
        // This is AUDIT acceptance - check department, not admin role
        if (userDepartment !== 'AUDIT' && userRole !== 'admin') {
            return {
                isValid: false,
                reason: `Only AUDIT department can accept vouchers. User department: ${userDepartment}`
            };
        }
    }
    else {
        // For other transitions, check the original role requirements
        const allowedRoles = transition.requiredRole;
        if (!allowedRoles.includes(userRole)) {
            return {
                isValid: false,
                reason: `User role ${userRole} not authorized for this transition. Required roles: ${transition.requiredRole.join(', ')}`
            };
        }
    }
    // Check required flags
    for (const [flag, required] of Object.entries(transition.requiredFlags)) {
        if (required && !flags[flag]) {
            return {
                isValid: false,
                reason: `Required flag "${flag}" not set for this transition`
            };
        }
    }
    return { isValid: true };
}
// Get the flags that should be set for a given status
function getFlagsForStatus(status) {
    const defaultFlags = {
        sentToAudit: false,
        dispatched: false,
        certified: false,
        rejected: false,
        isReturned: false,
        pendingReturn: false,
        receivedByAudit: false
    };
    switch (status) {
        case exports.VOUCHER_STATUSES.PENDING:
            return { ...defaultFlags };
        case exports.VOUCHER_STATUSES.PENDING_SUBMISSION:
            return { ...defaultFlags };
        case exports.VOUCHER_STATUSES.PENDING_RECEIPT:
            return { ...defaultFlags, sentToAudit: true };
        case exports.VOUCHER_STATUSES.AUDIT_PROCESSING:
            return { ...defaultFlags, sentToAudit: true, receivedByAudit: true };
        case exports.VOUCHER_STATUSES.VOUCHER_CERTIFIED:
            return { ...defaultFlags, sentToAudit: true, dispatched: true, certified: true, receivedByAudit: true };
        case exports.VOUCHER_STATUSES.VOUCHER_REJECTED:
            return { ...defaultFlags, sentToAudit: true, dispatched: true, rejected: true, receivedByAudit: true };
        case exports.VOUCHER_STATUSES.REJECTED_PENDING_DISPATCH:
            return { ...defaultFlags, sentToAudit: true, rejected: true, receivedByAudit: true };
        case exports.VOUCHER_STATUSES.VOUCHER_PENDING_RETURN:
            return { ...defaultFlags, sentToAudit: true, pendingReturn: true, receivedByAudit: true };
        case exports.VOUCHER_STATUSES.VOUCHER_RETURNED:
            return { ...defaultFlags, sentToAudit: true, isReturned: true, receivedByAudit: true };
        case exports.VOUCHER_STATUSES.VOUCHER_PROCESSING:
            return { ...defaultFlags, sentToAudit: true, dispatched: true, receivedByAudit: true };
        default:
            return { ...defaultFlags };
    }
}
// Synchronize voucher flags with its status
function synchronizeVoucherFlags(voucher) {
    const flags = getFlagsForStatus(voucher.status);
    return { status: voucher.status, flags };
}
// Get next allowed statuses based on current status and user role
function getNextAllowedStatuses(currentStatus, userRole, flags) {
    const transition = exports.VALID_STATUS_TRANSITIONS[currentStatus];
    if (!transition)
        return [];
    return transition.allowedTransitions.filter(newStatus => {
        const { isValid } = isValidStatusTransition(currentStatus, newStatus, userRole, flags);
        return isValid;
    });
}
// Log status transition
function logStatusTransition(voucherId, fromStatus, toStatus, userId, reason) {
    logger_js_1.logger.info({
        message: 'Voucher status transition',
        voucherId,
        fromStatus,
        toStatus,
        userId,
        reason
    });
}
//# sourceMappingURL=voucherStatusFlow.js.map