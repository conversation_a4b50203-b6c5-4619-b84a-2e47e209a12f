import { useState, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { useSocket } from '@/hooks/use-socket';
import { useAppStore } from '@/lib/store/app-store';

export interface BatchOperationLock {
  operationType: 'batch-dispatch' | 'batch-receive' | 'bulk-operation';
  department: string;
  voucherIds?: string[];
  lockKey: string;
  isActive: boolean;
}

/**
 * Smart Background Locking Hook for Finance Department
 * 
 * Provides automatic, invisible locking for batch operations without changing
 * existing workflow or UI. Only shows notifications when conflicts occur.
 */
export function useSmartBatchLocking(department: string) {
  const [activeLocks, setActiveLocks] = useState<Map<string, BatchOperationLock>>(new Map());
  const [isOperationInProgress, setIsOperationInProgress] = useState(false);
  const { toast } = useToast();
  const { socket } = useSocket();
  const currentUser = useAppStore(state => state.currentUser);

  /**
   * Generate a unique lock key for the operation
   */
  const generateLockKey = useCallback((operationType: string, department: string, suffix?: string): string => {
    const timestamp = Date.now();
    const baseLockKey = `${operationType}:${department}`;
    return suffix ? `${baseLockKey}:${suffix}:${timestamp}` : `${baseLockKey}:${timestamp}`;
  }, []);

  /**
   * Attempt to acquire a batch operation lock
   */
  const acquireBatchLock = useCallback(async (
    operationType: 'batch-dispatch' | 'batch-receive' | 'bulk-operation',
    voucherIds?: string[]
  ): Promise<{ success: boolean; lockKey?: string }> => {
    if (!socket || !currentUser) {
      return { success: false };
    }

    const lockKey = generateLockKey(operationType, department, voucherIds?.join('-'));
    
    return new Promise((resolve) => {
      // Request lock from server
      socket.emit('lock_request', {
        resourceType: 'batch-operation',
        resourceId: lockKey,
        targetDepartment: department
      }, (response: { success: boolean; message?: string }) => {
        if (response.success) {
          // Lock acquired successfully
          const batchLock: BatchOperationLock = {
            operationType,
            department,
            voucherIds,
            lockKey,
            isActive: true
          };

          setActiveLocks(prev => new Map(prev.set(lockKey, batchLock)));
          
          console.log(`🔒 SMART LOCK: Acquired ${operationType} lock for ${department}:`, lockKey);
          resolve({ success: true, lockKey });
        } else {
          // Lock acquisition failed - another user is performing similar operation
          console.log(`❌ SMART LOCK: Failed to acquire ${operationType} lock:`, response.message);
          
          // Show minimal notification to user
          toast({
            title: "Please Wait",
            description: response.message || "Another user is performing a similar operation.",
            variant: "default",
          });
          
          resolve({ success: false });
        }
      });
    });
  }, [socket, currentUser, department, generateLockKey, toast]);

  /**
   * Release a batch operation lock
   */
  const releaseBatchLock = useCallback(async (lockKey: string): Promise<void> => {
    if (!socket) return;

    return new Promise((resolve) => {
      socket.emit('lock_release', {
        resourceType: 'batch-operation',
        resourceId: lockKey
      }, (response: { success: boolean }) => {
        if (response.success) {
          setActiveLocks(prev => {
            const newLocks = new Map(prev);
            newLocks.delete(lockKey);
            return newLocks;
          });
          
          console.log(`🔓 SMART LOCK: Released lock:`, lockKey);
        }
        resolve();
      });
    });
  }, [socket]);

  /**
   * Execute a batch operation with automatic locking
   */
  const executeWithBatchLock = useCallback(async <T>(
    operationType: 'batch-dispatch' | 'batch-receive' | 'bulk-operation',
    operation: () => Promise<T>,
    voucherIds?: string[]
  ): Promise<T | null> => {
    if (isOperationInProgress) {
      toast({
        title: "Operation in Progress",
        description: "Please wait for the current operation to complete.",
        variant: "default",
      });
      return null;
    }

    setIsOperationInProgress(true);

    try {
      // Attempt to acquire lock
      const lockResult = await acquireBatchLock(operationType, voucherIds);
      
      if (!lockResult.success) {
        // Lock acquisition failed - user was notified by acquireBatchLock
        return null;
      }

      // Execute the operation
      console.log(`🚀 SMART LOCK: Executing ${operationType} with lock protection`);
      const result = await operation();
      
      // Release lock after successful operation
      if (lockResult.lockKey) {
        await releaseBatchLock(lockResult.lockKey);
      }
      
      return result;
    } catch (error) {
      console.error(`❌ SMART LOCK: Error during ${operationType}:`, error);
      
      // Release lock on error
      const activeLockEntries = Array.from(activeLocks.entries());
      const currentLock = activeLockEntries.find(([_, lock]) => lock.operationType === operationType);
      if (currentLock) {
        await releaseBatchLock(currentLock[0]);
      }
      
      throw error;
    } finally {
      setIsOperationInProgress(false);
    }
  }, [isOperationInProgress, acquireBatchLock, releaseBatchLock, activeLocks, toast]);

  /**
   * Check if a specific operation type is currently locked
   */
  const isOperationLocked = useCallback((operationType: 'batch-dispatch' | 'batch-receive' | 'bulk-operation'): boolean => {
    return Array.from(activeLocks.values()).some(lock => 
      lock.operationType === operationType && lock.isActive
    );
  }, [activeLocks]);

  return {
    executeWithBatchLock,
    isOperationLocked,
    isOperationInProgress,
    activeLocks: Array.from(activeLocks.values())
  };
}
