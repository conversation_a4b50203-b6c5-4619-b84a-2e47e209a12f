import mysql from 'mysql2/promise';
import dotenv from 'dotenv';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger.js';

dotenv.config();

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'vms@2025@1989',
  database: process.env.DB_NAME || 'vms_production',
  waitForConnections: true,
  connectionLimit: 20,
  queueLimit: 0,
  enableKeepAlive: true,
  keepAliveInitialDelay: 0
};

// Create connection pool
const pool = mysql.createPool(dbConfig);

// Test pool connection
pool.getConnection()
  .then(connection => {
    logger.info('Database connection pool initialized');
    connection.release();
  })
  .catch(err => {
    logger.error('Error initializing database pool:', err);
    process.exit(1);
  });

// Initialize database
export async function initializeDatabase() {
  try {
    // Test connection
    const connection = await pool.getConnection();
    logger.info('Connected to MySQL database');
    connection.release();

    // Create database if it doesn't exist
    await createDatabaseIfNotExists();

    // Create tables if they don't exist
    await createTables();

    return true;
  } catch (error) {
    logger.error('Database initialization error:', error);
    throw error;
  }
}

// Create database if it doesn't exist
async function createDatabaseIfNotExists() {
  try {
    const connection = await mysql.createConnection({
      host: dbConfig.host,
      port: dbConfig.port,
      user: dbConfig.user,
      password: dbConfig.password,
    });

    await connection.query(`CREATE DATABASE IF NOT EXISTS ${dbConfig.database}`);
    await connection.end();
    logger.info(`Database '${dbConfig.database}' created or already exists`);
  } catch (error) {
    logger.error('Error creating database:', error);
    throw error;
  }
}

// Create tables if they don't exist
async function createTables() {
  try {
    // Create users table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS users (
        id VARCHAR(36) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        password VARCHAR(255) NOT NULL,
        role ENUM('admin', 'manager', 'operator', 'viewer', 'USER') NOT NULL,
        department VARCHAR(50) NOT NULL,
        date_created DATETIME NOT NULL,
        last_login DATETIME,
        is_active BOOLEAN DEFAULT TRUE,
        email VARCHAR(255)
      )
    `);

    // Create vouchers table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS vouchers (
        id VARCHAR(36) PRIMARY KEY,
        voucher_id VARCHAR(50) NOT NULL UNIQUE,
        date VARCHAR(50) NOT NULL,
        claimant VARCHAR(255) NOT NULL,
        description TEXT NOT NULL,
        amount DECIMAL(15, 2) NOT NULL,
        currency ENUM('GHS', 'USD', 'GBP', 'EUR') NOT NULL,
        department VARCHAR(50) NOT NULL,
        dispatched_by VARCHAR(255),
        dispatch_time VARCHAR(50),
        status VARCHAR(50) NOT NULL,
        sent_to_audit BOOLEAN DEFAULT FALSE,
        created_by VARCHAR(255) NOT NULL,
        batch_id VARCHAR(36),
        received_by VARCHAR(255),
        receipt_time VARCHAR(50),
        comment TEXT,
        tax_type VARCHAR(50),
        tax_details TEXT,
        tax_amount DECIMAL(15, 2),
        pre_audited_amount DECIMAL(15, 2),
        pre_audited_by VARCHAR(255),
        certified_by VARCHAR(255),
        audit_dispatch_time VARCHAR(50),
        audit_dispatched_by VARCHAR(255),
        dispatch_to_on_department BOOLEAN DEFAULT FALSE,
        post_provisional_cash BOOLEAN DEFAULT FALSE,
        dispatched BOOLEAN DEFAULT FALSE,
        dispatch_to_audit_by VARCHAR(255),
        work_started BOOLEAN DEFAULT FALSE,
        is_returned BOOLEAN DEFAULT FALSE,
        return_comment TEXT,
        return_time VARCHAR(50),
        deleted BOOLEAN DEFAULT FALSE,
        deletion_time VARCHAR(50),
        rejection_time VARCHAR(50),
        department_receipt_time VARCHAR(50),
        department_received_by VARCHAR(255),
        department_rejected BOOLEAN DEFAULT FALSE,
        rejected_by VARCHAR(255),
        pending_return BOOLEAN DEFAULT FALSE,
        return_initiated_time VARCHAR(50),
        reference_id VARCHAR(50),
        rejected BOOLEAN DEFAULT FALSE,
        certified BOOLEAN DEFAULT FALSE,
        idempotency_key VARCHAR(255),
        received_by_audit BOOLEAN DEFAULT FALSE,
        original_department VARCHAR(50),
        flags TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_idempotency (created_by, original_department, idempotency_key)
      )
    `);

    // Create voucher_batches table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS voucher_batches (
        id VARCHAR(36) PRIMARY KEY,
        department VARCHAR(50) NOT NULL,
        sent_by VARCHAR(255) NOT NULL,
        sent_time VARCHAR(50) NOT NULL,
        received BOOLEAN DEFAULT FALSE,
        from_audit BOOLEAN DEFAULT FALSE
      )
    `);

    // Create batch_vouchers table (junction table for many-to-many relationship)
    await pool.query(`
      CREATE TABLE IF NOT EXISTS batch_vouchers (
        batch_id VARCHAR(36) NOT NULL,
        voucher_id VARCHAR(36) NOT NULL,
        PRIMARY KEY (batch_id, voucher_id),
        FOREIGN KEY (batch_id) REFERENCES voucher_batches(id) ON DELETE CASCADE,
        FOREIGN KEY (voucher_id) REFERENCES vouchers(id) ON DELETE CASCADE
      )
    `);

    // Create provisional_cash_records table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS provisional_cash_records (
        id VARCHAR(36) PRIMARY KEY,
        voucher_id VARCHAR(36) NOT NULL,
        voucher_ref VARCHAR(50) NOT NULL,
        claimant VARCHAR(255) NOT NULL,
        description TEXT NOT NULL,
        main_amount DECIMAL(15, 2) NOT NULL,
        currency ENUM('GHS', 'USD', 'GBP', 'EUR') NOT NULL,
        amount_retired DECIMAL(15, 2),
        clearance_remark ENUM('CLEARED', 'REFUNDED TO CHEST', 'DUE STAFF', 'RETURNED'),
        date_retired VARCHAR(50),
        cleared_by VARCHAR(255),
        comment TEXT,
        date VARCHAR(50) NOT NULL,
        FOREIGN KEY (voucher_id) REFERENCES vouchers(id) ON DELETE CASCADE
      )
    `);

    // Create notifications table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS notifications (
        id VARCHAR(36) PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        message TEXT NOT NULL,
        is_read BOOLEAN DEFAULT FALSE,
        timestamp VARCHAR(50) NOT NULL,
        voucher_id VARCHAR(36),
        batch_id VARCHAR(36),
        type VARCHAR(50) NOT NULL,
        from_audit BOOLEAN DEFAULT FALSE
      )
    `);

    // Create blacklisted_voucher_ids table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS blacklisted_voucher_ids (
        id VARCHAR(36) PRIMARY KEY,
        voucher_id VARCHAR(50) NOT NULL UNIQUE
      )
    `);

    // Create pending_registrations table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS pending_registrations (
        id VARCHAR(36) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        password VARCHAR(255) NOT NULL,
        department VARCHAR(50) NOT NULL,
        date_requested VARCHAR(50) NOT NULL,
        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending'
      )
    `);

    // Create system_settings table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS system_settings (
        id INT PRIMARY KEY AUTO_INCREMENT,
        fiscal_year_start ENUM('JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC') DEFAULT 'JAN',
        fiscal_year_end ENUM('JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC') DEFAULT 'DEC',
        current_fiscal_year INT NOT NULL,
        system_time VARCHAR(50) NOT NULL,
        auto_backup_enabled BOOLEAN DEFAULT TRUE,
        session_timeout INT DEFAULT 30,
        last_backup_date VARCHAR(50)
      )
    `);

    // Create resource_locks table for concurrency management
    await pool.query(`
      CREATE TABLE IF NOT EXISTS resource_locks (
        id VARCHAR(36) PRIMARY KEY,
        resource_type VARCHAR(50) NOT NULL,
        resource_id VARCHAR(36) NOT NULL,
        user_id VARCHAR(36) NOT NULL,
        department VARCHAR(50) NOT NULL,
        lock_time DATETIME NOT NULL,
        expiry_time DATETIME NOT NULL,
        UNIQUE KEY unique_resource (resource_type, resource_id)
      )
    `);

    // Create audit_logs table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS audit_logs (
        id VARCHAR(36) PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        action VARCHAR(255) NOT NULL,
        resource_type VARCHAR(50) NOT NULL,
        resource_id VARCHAR(36),
        details TEXT,
        timestamp DATETIME NOT NULL,
        ip_address VARCHAR(50)
      )
    `);

    // Create active_sessions table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS active_sessions (
        id VARCHAR(36) PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        user_name VARCHAR(255),
        department VARCHAR(50),
        socket_id VARCHAR(100),
        session_start DATETIME NOT NULL,
        last_activity DATETIME NOT NULL,
        session_end DATETIME,
        client_ip VARCHAR(50),
        user_agent TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        selected_year INT,
        selected_database VARCHAR(100),
        INDEX (user_id),
        INDEX (is_active),
        INDEX (department)
      )
    `);

    // Create audit_logs table for comprehensive activity tracking
    await pool.query(`
      CREATE TABLE IF NOT EXISTS audit_logs (
        id VARCHAR(36) PRIMARY KEY,
        timestamp DATETIME NOT NULL,
        user_id VARCHAR(36) NOT NULL,
        user_name VARCHAR(255) NOT NULL,
        department VARCHAR(50) NOT NULL,
        action VARCHAR(100) NOT NULL,
        description TEXT NOT NULL,
        resource_type VARCHAR(50) NOT NULL,
        resource_id VARCHAR(255),
        details JSON,
        ip_address VARCHAR(50),
        user_agent TEXT,
        severity ENUM('INFO', 'WARNING', 'ERROR') DEFAULT 'INFO',
        INDEX (timestamp),
        INDEX (user_id),
        INDEX (department),
        INDEX (action),
        INDEX (resource_type),
        INDEX (severity)
      )
    `);

    // Create voucher_logs table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS voucher_logs (
        id VARCHAR(36) PRIMARY KEY,
        voucher_id VARCHAR(36) NOT NULL,
        from_status VARCHAR(50) NOT NULL,
        to_status VARCHAR(50) NOT NULL,
        user_id VARCHAR(36) NOT NULL,
        comment TEXT,
        created_at DATETIME NOT NULL,
        FOREIGN KEY (voucher_id) REFERENCES vouchers(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `);

    // PRODUCTION: Add idempotency_key column if it doesn't exist
    try {
      await pool.query(`
        ALTER TABLE vouchers
        ADD COLUMN idempotency_key VARCHAR(255)
      `);
    } catch (error: any) {
      // Ignore error if column already exists
      if (error.code !== 'ER_DUP_FIELDNAME') {
        throw error;
      }
    }

    // YEAR MANAGEMENT: Add year selection columns to active_sessions if they don't exist
    try {
      await pool.query(`
        ALTER TABLE active_sessions
        ADD COLUMN selected_year INT
      `);
    } catch (error: any) {
      // Ignore error if column already exists
      if (error.code !== 'ER_DUP_FIELDNAME') {
        console.warn('Could not add selected_year column:', error.message);
      }
    }

    try {
      await pool.query(`
        ALTER TABLE active_sessions
        ADD COLUMN selected_database VARCHAR(100)
      `);
    } catch (error: any) {
      // Ignore error if column already exists
      if (error.code !== 'ER_DUP_FIELDNAME') {
        console.warn('Could not add selected_database column:', error.message);
      }
    }

    // PRODUCTION: Add flags column if it doesn't exist
    try {
      await pool.query(`
        ALTER TABLE vouchers
        ADD COLUMN flags JSON
      `);
      logger.info('Added flags column to vouchers table');

      // Initialize flags for existing vouchers that don't have them
      await pool.query(`
        UPDATE vouchers
        SET flags = JSON_OBJECT(
          'sentToAudit', COALESCE(sent_to_audit, FALSE),
          'dispatched', FALSE,
          'certified', FALSE,
          'rejected', FALSE,
          'isReturned', COALESCE(is_returned, FALSE),
          'pendingReturn', FALSE,
          'receivedByAudit', FALSE
        )
        WHERE flags IS NULL
      `);
      logger.info('Initialized flags for existing vouchers');

    } catch (error: any) {
      // Ignore error if column already exists
      if (error.code !== 'ER_DUP_FIELDNAME') {
        throw error;
      }
    }

    // CRITICAL FIX: Add original_department column to track where vouchers originally came from
    try {
      await pool.query(`
        ALTER TABLE vouchers
        ADD COLUMN original_department VARCHAR(50)
      `);
      logger.info('Added original_department column to vouchers table');

      // Initialize original_department for existing vouchers based on current department
      await pool.query(`
        UPDATE vouchers
        SET original_department = department
        WHERE original_department IS NULL
      `);
      logger.info('Initialized original_department for existing vouchers');

    } catch (error: any) {
      // Ignore error if column already exists
      if (error.code !== 'ER_DUP_FIELDNAME') {
        throw error;
      }
    }

    // CRITICAL FIX: Add received_by_audit column for audit workflow tracking
    try {
      await pool.query(`
        ALTER TABLE vouchers
        ADD COLUMN received_by_audit BOOLEAN DEFAULT FALSE
      `);
      logger.info('Added received_by_audit column to vouchers table');

    } catch (error: any) {
      // Ignore error if column already exists
      if (error.code !== 'ER_DUP_FIELDNAME') {
        throw error;
      }
    }

    // CRITICAL FIX: Add work_started column for audit workflow tracking
    try {
      await pool.query(`
        ALTER TABLE vouchers
        ADD COLUMN work_started BOOLEAN DEFAULT FALSE
      `);
      logger.info('Added work_started column to vouchers table');

    } catch (error: any) {
      // Ignore error if column already exists
      if (error.code !== 'ER_DUP_FIELDNAME') {
        throw error;
      }
    }

    // Add index if it doesn't exist
    try {
      await pool.query(`
        CREATE INDEX idx_idempotency ON vouchers (created_by, original_department, idempotency_key)
      `);
    } catch (error: any) {
      // Ignore error if index already exists
      if (error.code !== 'ER_DUP_KEYNAME') {
        throw error;
      }
    }

    logger.info('Database tables created or already exist');

    // CRITICAL FIX: Initialize default users if none exist
    await initializeDefaultUsers();
  } catch (error) {
    logger.error('Error creating tables:', error);
    throw error;
  }
}

// Initialize default users for all departments
async function initializeDefaultUsers() {
  try {
    // Check if any users exist
    const existingUsers = await query('SELECT COUNT(*) as count FROM users') as any[];
    const userCount = existingUsers[0]?.count || 0;

    if (userCount === 0) {
      logger.info('No users found, initializing default users for all departments...');

      const defaultUsers = [
        // System Admin only - other users will be created through registration
        { id: uuidv4(), name: 'ADMIN', department: 'SYSTEM ADMIN', role: 'admin', password: 'enter123' }
      ];

      for (const user of defaultUsers) {
        await query(
          'INSERT INTO users (id, name, department, role, password, is_active, date_created) VALUES (?, ?, ?, ?, ?, ?, NOW())',
          [user.id, user.name, user.department, user.role, user.password, true]
        );
        logger.info(`✅ Created default user: ${user.name} (${user.department})`);
      }

      logger.info(`🎉 Successfully created ${defaultUsers.length} default users`);
    } else {
      logger.info(`Users already exist (${userCount} users found)`);
    }
  } catch (error) {
    logger.error('Error initializing default users:', error);
    // Don't throw - this shouldn't prevent server startup
  }
}

// Wrapper for transactions
export async function withTransaction<T>(callback: (connection: mysql.PoolConnection) => Promise<T>): Promise<T> {
  const connection = await pool.getConnection();
  try {
    await connection.beginTransaction();
    const result = await callback(connection);
    await connection.commit();
    return result;
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
}

// Query with automatic connection management
export async function query<T = any>(sql: string, params?: any[]): Promise<T[]> {
  try {
    const [results] = await pool.execute(sql, params);
    return results as T[];
  } catch (error) {
    logger.error('Database query error:', error);
    throw error;
  }
}

// Get transaction for manual transaction management
export async function getTransaction(): Promise<mysql.PoolConnection> {
  return await pool.getConnection();
}

export default pool;
