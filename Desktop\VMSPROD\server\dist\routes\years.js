"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.yearRouter = void 0;
const express_1 = __importDefault(require("express"));
const db_js_1 = require("../database/db.js");
const logger_js_1 = require("../utils/logger.js");
const auth_js_1 = require("../middleware/auth.js");
const yearRouter = express_1.default.Router();
exports.yearRouter = yearRouter;
// Apply authentication middleware to all routes
yearRouter.use(auth_js_1.authenticate);
// Get available years with statistics
yearRouter.get('/available', async (req, res) => {
    try {
        logger_js_1.logger.info('Fetching available years for user:', req.user?.name);
        // For now, return current year data since multi-year databases might not be set up
        const currentYear = new Date().getFullYear();
        const currentDb = process.env.DB_NAME || 'vms_production';
        // Get statistics for current year
        const voucherStats = await (0, db_js_1.query)(`
      SELECT
        COUNT(*) as voucher_count,
        COALESCE(SUM(amount), 0) as total_amount
      FROM vouchers
    `);
        // Get active departments
        const departments = await (0, db_js_1.query)(`
      SELECT DISTINCT department
      FROM vouchers
      WHERE department IS NOT NULL
      ORDER BY department
    `);
        // Get last activity
        const lastActivity = await (0, db_js_1.query)(`
      SELECT MAX(created_at) as last_activity
      FROM vouchers
    `);
        // Check if this year has recent activity (within last 30 days)
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const recentActivity = await (0, db_js_1.query)(`
      SELECT COUNT(*) as recent_count
      FROM vouchers
      WHERE created_at >= ?
    `, [thirtyDaysAgo.toISOString().slice(0, 19).replace('T', ' ')]);
        const yearData = [{
                year: currentYear,
                voucherCount: parseInt(voucherStats[0]?.voucher_count || '0'),
                totalAmount: parseFloat(voucherStats[0]?.total_amount || '0'),
                departments: departments.map((d) => d.department),
                isActive: (parseInt(recentActivity[0]?.recent_count || '0')) > 0,
                lastActivity: lastActivity[0]?.last_activity || new Date().toISOString()
            }];
        logger_js_1.logger.info(`Found ${yearData.length} available years`);
        res.json(yearData);
    }
    catch (error) {
        logger_js_1.logger.error('Error fetching available years:', error);
        res.status(500).json({ error: 'Failed to fetch available years' });
    }
});
// Set active year for user session
yearRouter.post('/select', async (req, res) => {
    try {
        const { year } = req.body;
        const userId = req.user?.id;
        if (!year || !userId) {
            return res.status(400).json({ error: 'Year and user ID are required' });
        }
        // Validate year format
        if (!Number.isInteger(year) || year < 2020 || year > 2030) {
            return res.status(400).json({ error: 'Invalid year format' });
        }
        // Check if database for this year exists
        const databases = await (0, db_js_1.query)('SHOW DATABASES');
        const targetDb = `vms_${year}`;
        const dbExists = databases.some((db) => db.Database === targetDb);
        if (!dbExists && year !== new Date().getFullYear()) {
            return res.status(404).json({ error: `Database for year ${year} not found` });
        }
        // Store selected year in active_sessions table (VMS uses custom session system)
        const sessionId = req.user?.sessionId;
        if (sessionId) {
            await (0, db_js_1.query)('UPDATE active_sessions SET selected_year = ?, selected_database = ? WHERE id = ?', [year, dbExists ? targetDb : (process.env.DB_NAME || 'vms_production'), sessionId]);
        }
        // Log the year selection
        logger_js_1.logger.info(`User ${req.user?.name} selected year ${year}`);
        // Update user's last selected year in database
        try {
            await (0, db_js_1.query)('UPDATE users SET last_selected_year = ? WHERE id = ?', [year, userId]);
        }
        catch (updateError) {
            // If column doesn't exist, we'll add it later
            logger_js_1.logger.warn('Could not update last_selected_year:', updateError);
        }
        res.json({
            success: true,
            selectedYear: year,
            database: dbExists ? targetDb : (process.env.DB_NAME || 'vms_production'),
            message: `Successfully selected year ${year}`
        });
    }
    catch (error) {
        logger_js_1.logger.error('Error selecting year:', error);
        res.status(500).json({ error: 'Failed to select year' });
    }
});
// Get current selected year
yearRouter.get('/current', async (req, res) => {
    try {
        // Get selected year from active_sessions table
        const sessionId = req.user?.sessionId;
        let selectedYear = new Date().getFullYear();
        let selectedDatabase = process.env.DB_NAME || 'vms_production';
        if (sessionId) {
            const sessions = await (0, db_js_1.query)('SELECT selected_year, selected_database FROM active_sessions WHERE id = ?', [sessionId]);
            if (sessions.length > 0 && sessions[0].selected_year) {
                selectedYear = sessions[0].selected_year;
                selectedDatabase = sessions[0].selected_database || selectedDatabase;
            }
        }
        res.json({
            selectedYear,
            selectedDatabase,
            currentYear: new Date().getFullYear()
        });
    }
    catch (error) {
        logger_js_1.logger.error('Error getting current year:', error);
        res.status(500).json({ error: 'Failed to get current year' });
    }
});
// Create new year database (Admin only)
yearRouter.post('/create', async (req, res) => {
    try {
        const { year } = req.body;
        const userRole = req.user?.role;
        // Check if user is admin
        if (userRole !== 'ADMIN') {
            return res.status(403).json({ error: 'Admin access required' });
        }
        if (!year || !Number.isInteger(year)) {
            return res.status(400).json({ error: 'Valid year is required' });
        }
        const newDbName = `vms_${year}`;
        // Check if database already exists
        const databases = await (0, db_js_1.query)('SHOW DATABASES');
        const dbExists = databases.some((db) => db.Database === newDbName);
        if (dbExists) {
            return res.status(409).json({ error: `Database for year ${year} already exists` });
        }
        // Create new database
        await (0, db_js_1.query)(`CREATE DATABASE ${newDbName}`);
        await (0, db_js_1.query)(`USE ${newDbName}`);
        // Copy table structure from current database
        const originalDb = process.env.DB_NAME || 'vms_production';
        // Get table creation statements from original database
        const tables = [
            'users', 'vouchers', 'voucher_batches', 'batch_vouchers',
            'provisional_cash_records', 'notifications', 'audit_logs',
            'voucher_logs', 'active_sessions'
        ];
        for (const table of tables) {
            try {
                const createStatement = await (0, db_js_1.query)(`SHOW CREATE TABLE ${originalDb}.${table}`);
                if (createStatement.length > 0) {
                    const createSQL = createStatement[0]['Create Table'];
                    await (0, db_js_1.query)(createSQL);
                }
            }
            catch (tableError) {
                logger_js_1.logger.warn(`Could not copy table ${table}:`, tableError);
            }
        }
        // Copy users table data (users should exist across all years)
        await (0, db_js_1.query)(`INSERT INTO users SELECT * FROM ${originalDb}.users`);
        // Switch back to original database
        await (0, db_js_1.query)(`USE ${originalDb}`);
        logger_js_1.logger.info(`Created new year database: ${newDbName}`);
        res.json({
            success: true,
            database: newDbName,
            year: year,
            message: `Successfully created database for year ${year}`
        });
    }
    catch (error) {
        logger_js_1.logger.error('Error creating year database:', error);
        res.status(500).json({ error: 'Failed to create year database' });
    }
});
// Get year rollover status (accessible to all authenticated users)
yearRouter.get('/rollover/status', async (req, res) => {
    try {
        const { yearRolloverService } = await import('../services/year-rollover-service.js');
        const status = await yearRolloverService.getRolloverStatus();
        res.json(status);
    }
    catch (error) {
        logger_js_1.logger.error('Get rollover status error:', error);
        res.status(500).json({ error: 'Failed to get rollover status' });
    }
});
//# sourceMappingURL=years.js.map