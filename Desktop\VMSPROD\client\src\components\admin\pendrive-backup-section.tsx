import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { 
  HardDrive, 
  Download, 
  Calendar, 
  Clock, 
  FileText, 
  Database,
  Users,
  Settings,
  AlertTriangle,
  CheckCircle,
  Folder
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface DailyBackup {
  date: string;
  path: string;
  manifest: {
    metadata: {
      backupDate: string;
      backupType: string;
      year: number;
      database: string;
      createdBy: string;
      totalSize: number;
    };
    files: Array<{
      name: string;
      type: string;
      size: number;
      description: string;
    }>;
  } | null;
  files: number;
}

export function PendriveBackupSection() {
  const [isCreatingBackup, setIsCreatingBackup] = useState(false);
  const [dailyBackups, setDailyBackups] = useState<DailyBackup[]>([]);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());

  // Load available daily backups
  useEffect(() => {
    loadDailyBackups();
  }, []);

  const loadDailyBackups = async () => {
    try {
      const response = await fetch('/api/pendrive-backup/daily-backups', {
        credentials: 'include'
      });
      
      if (response.ok) {
        const backups = await response.json();
        setDailyBackups(backups);
      }
    } catch (error) {
      console.error('Error loading daily backups:', error);
    }
  };

  const createDailyBackup = async () => {
    setIsCreatingBackup(true);
    
    try {
      const response = await fetch('/api/pendrive-backup/create-daily-backup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({ year: selectedYear })
      });

      if (response.ok) {
        const result = await response.json();
        
        toast({
          title: "Daily Backup Created",
          description: `Backup for ${result.backup.date} created successfully`,
          variant: "default"
        });

        // Reload backups list
        await loadDailyBackups();
        
        // Show instructions
        toast({
          title: "Pendrive Instructions",
          description: `Copy folder: daily-backups/${result.backup.date}/ to your pendrive`,
          variant: "default"
        });

      } else {
        const error = await response.json();
        throw new Error(error.error || 'Backup creation failed');
      }
    } catch (error) {
      toast({
        title: "Backup Failed",
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: "destructive"
      });
    } finally {
      setIsCreatingBackup(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileTypeIcon = (type: string) => {
    switch (type) {
      case 'database': return <Database className="h-4 w-4" />;
      case 'settings': return <Settings className="h-4 w-4" />;
      case 'users': return <Users className="h-4 w-4" />;
      case 'statistics': return <FileText className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const today = new Date().toISOString().split('T')[0];
  const todayBackup = dailyBackups.find(backup => backup.date === today);

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <HardDrive className="h-6 w-6" />
        <h2 className="text-2xl font-bold">Pendrive Backup System</h2>
      </div>

      {/* Create Daily Backup */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Download className="mr-2 h-5 w-5" />
            End-of-Day Backup
          </CardTitle>
          <CardDescription>
            Create comprehensive backup for pendrive storage
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">Backup Year</label>
              <select 
                value={selectedYear} 
                onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                className="w-full mt-1 p-2 border rounded-md"
                disabled={isCreatingBackup}
              >
                <option value={2025}>2025 (Current)</option>
                <option value={2024}>2024 (Historical)</option>
                <option value={2023}>2023 (Historical)</option>
              </select>
            </div>
            
            <div>
              <label className="text-sm font-medium">Backup Date</label>
              <div className="flex items-center mt-1 p-2 border rounded-md bg-gray-50">
                <Calendar className="h-4 w-4 mr-2" />
                <span>{new Date().toLocaleDateString()}</span>
              </div>
            </div>
          </div>

          {todayBackup && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertTitle>Today's Backup Available</AlertTitle>
              <AlertDescription>
                A backup for today ({today}) already exists. Creating a new backup will replace it.
              </AlertDescription>
            </Alert>
          )}

          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Backup Contents</AlertTitle>
            <AlertDescription>
              This backup includes: Complete database, system settings, user accounts, and daily statistics.
              Perfect for end-of-day pendrive storage.
            </AlertDescription>
          </Alert>

          <Button
            onClick={createDailyBackup}
            disabled={isCreatingBackup}
            className="w-full"
            size="lg"
          >
            {isCreatingBackup ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                Creating Daily Backup...
              </>
            ) : (
              <>
                <HardDrive className="mr-2 h-4 w-4" />
                Create End-of-Day Backup
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Available Daily Backups */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Folder className="mr-2 h-5 w-5" />
            Available Daily Backups
          </CardTitle>
          <CardDescription>
            Recent end-of-day backups ready for pendrive
          </CardDescription>
        </CardHeader>
        <CardContent>
          {dailyBackups.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <HardDrive className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No daily backups available</p>
              <p className="text-sm">Create your first end-of-day backup above</p>
            </div>
          ) : (
            <div className="space-y-4">
              {dailyBackups.slice(0, 7).map((backup) => (
                <div key={backup.date} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <Calendar className="h-5 w-5 text-blue-600" />
                      <div>
                        <h4 className="font-medium">{backup.date}</h4>
                        <p className="text-sm text-muted-foreground">
                          {backup.manifest?.metadata.createdBy && (
                            <>Created by {backup.manifest.metadata.createdBy}</>
                          )}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      {backup.date === today && (
                        <Badge variant="default" className="bg-green-600">Today</Badge>
                      )}
                      <Badge variant="outline">
                        {backup.files} files
                      </Badge>
                      {backup.manifest && (
                        <Badge variant="outline">
                          {formatFileSize(backup.manifest.metadata.totalSize)}
                        </Badge>
                      )}
                    </div>
                  </div>

                  {backup.manifest && (
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mt-3">
                      {backup.manifest.files.map((file, index) => (
                        <div key={index} className="flex items-center space-x-2 text-sm">
                          {getFileTypeIcon(file.type)}
                          <div>
                            <p className="font-medium">{file.type.toUpperCase()}</p>
                            <p className="text-muted-foreground text-xs">
                              {formatFileSize(file.size)}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  <div className="mt-3 p-3 bg-blue-50 rounded-md">
                    <p className="text-sm font-medium text-blue-900">
                      📁 Pendrive Location: <code>daily-backups/{backup.date}/</code>
                    </p>
                    <p className="text-xs text-blue-700 mt-1">
                      Copy this entire folder to your pendrive for safe storage
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="mr-2 h-5 w-5" />
            Pendrive Backup Instructions
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">📥 Creating Backup</h4>
              <ol className="text-sm space-y-1 list-decimal list-inside">
                <li>Click "Create End-of-Day Backup"</li>
                <li>Wait for backup completion</li>
                <li>Navigate to server folder: <code>daily-backups/[date]/</code></li>
                <li>Copy entire folder to pendrive</li>
                <li>Safely eject pendrive</li>
              </ol>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">📤 Restoring from Pendrive</h4>
              <ol className="text-sm space-y-1 list-decimal list-inside">
                <li>Insert pendrive with backup</li>
                <li>Copy backup folder to server</li>
                <li>Go to Admin Panel &gt; Restore</li>
                <li>Select database backup file (.sql)</li>
                <li>Follow restore instructions</li>
              </ol>
            </div>
          </div>

          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Security Reminder</AlertTitle>
            <AlertDescription>
              Pendrive backups contain sensitive financial data. Store in a secure location and encrypt if possible.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    </div>
  );
}
