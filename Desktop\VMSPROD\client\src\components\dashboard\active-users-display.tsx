import { useEffect, useState, useCallback } from 'react';
import { getSocket } from '@/lib/socket';
import { useAppStore } from '@/lib/store';
import { createManagedInterval, clearManagedInterval } from '@/lib/interval-manager';
import { Badge } from '@/components/ui/badge';
import { Users, UserCheck } from 'lucide-react';
import { User } from '@/lib/store/types';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface ActiveUsersDisplayProps {
  department: string;
}

interface OnlineUser {
  id: string;
  name: string;
  department: string;
  lastName: string;
  loginTime: string;
}

// Helper function to extract last name from full name
function extractLastName(fullName: string): string {
  const nameParts = fullName.trim().split(' ');
  return nameParts[nameParts.length - 1].toUpperCase();
}

// Component for displaying online presence
function OnlinePresenceDisplay({ department }: ActiveUsersDisplayProps) {
  const [onlineUsers, setOnlineUsers] = useState<OnlineUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const currentUser = useAppStore((state) => state.currentUser);

  // Fetch online users from API
  const fetchOnlineUsers = useCallback(async () => {
    try {
      const response = await fetch(`/api/users/online?department=${department}`, {
        credentials: 'include'
      });

      if (response.ok) {
        const users = await response.json();
        const processedUsers = users.map((user: any) => ({
          id: user.id,
          name: user.name,
          department: user.department,
          lastName: extractLastName(user.name),
          loginTime: user.login_time || new Date().toISOString()
        }));
        setOnlineUsers(processedUsers);
      }
    } catch (error) {
      console.error('Error fetching online users:', error);
    } finally {
      setIsLoading(false);
    }
  }, [department]);

  // Set up real-time updates
  useEffect(() => {
    // Initial fetch
    fetchOnlineUsers();

    // Set up periodic refresh
    const intervalId = createManagedInterval(fetchOnlineUsers, 30000); // Refresh every 30 seconds

    // Set up WebSocket listeners for real-time updates
    const socket = getSocket();
    if (socket) {
      socket.on('user_online', (userData: any) => {
        if (userData.department === department) {
          const newUser: OnlineUser = {
            id: userData.id,
            name: userData.name,
            department: userData.department,
            lastName: extractLastName(userData.name),
            loginTime: userData.login_time || new Date().toISOString()
          };

          setOnlineUsers(prev => {
            const exists = prev.find(u => u.id === newUser.id);
            if (!exists) {
              return [...prev, newUser];
            }
            return prev;
          });
        }
      });

      socket.on('user_offline', (userData: any) => {
        if (userData.department === department) {
          setOnlineUsers(prev => prev.filter(u => u.id !== userData.id));
        }
      });
    }

    return () => {
      clearManagedInterval(intervalId);
      if (socket) {
        socket.off('user_online');
        socket.off('user_offline');
      }
    };
  }, [department, fetchOnlineUsers]);

  // Render online users display
  const displayUsers = onlineUsers.slice(0, 6); // Show max 6 users
  const additionalCount = Math.max(0, onlineUsers.length - 6);

  if (isLoading) {
    return (
      <div className="flex items-center space-x-2">
        <Users className="h-4 w-4 text-muted-foreground" />
        <span className="text-sm text-muted-foreground">Loading...</span>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className="flex items-center space-x-2">
        <Users className="h-4 w-4 text-green-600" />
        <div className="flex items-center space-x-1">
          {displayUsers.map((user, index) => (
            <Tooltip key={user.id}>
              <TooltipTrigger asChild>
                <Badge
                  variant="outline"
                  className={`text-xs px-2 py-1 ${
                    user.id === currentUser?.id
                      ? 'bg-green-100 border-green-500 text-green-700'
                      : 'bg-blue-100 border-blue-500 text-blue-700'
                  }`}
                >
                  {user.lastName}
                </Badge>
              </TooltipTrigger>
              <TooltipContent>
                <p>{user.name}</p>
                <p className="text-xs text-muted-foreground">
                  Online since {new Date(user.loginTime).toLocaleTimeString()}
                </p>
              </TooltipContent>
            </Tooltip>
          ))}

          {additionalCount > 0 && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Badge variant="outline" className="text-xs px-2 py-1 bg-gray-100 border-gray-500 text-gray-700">
                  +{additionalCount}
                </Badge>
              </TooltipTrigger>
              <TooltipContent>
                <p>{additionalCount} more user{additionalCount > 1 ? 's' : ''} online</p>
              </TooltipContent>
            </Tooltip>
          )}
        </div>
      </div>
    </TooltipProvider>
  );
}

// Main export - now shows online presence instead of editor status
export function ActiveUsersDisplay({ department }: ActiveUsersDisplayProps) {
  return <OnlinePresenceDisplay department={department} />;
}
