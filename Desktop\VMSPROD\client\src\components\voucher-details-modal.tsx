import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  CustomDialog,
  CustomDialogContent,
  CustomDialogDescription,
  CustomDialogHeader,
  CustomDialogTitle,
  CustomDialogFooter,
} from '@/components/custom-dialog';
import { X } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Voucher } from '@/lib/types';
import { useAppStore } from '@/lib/store';
import { formatVMSDateTime, formatNumberWithCommas } from '@/utils/formatUtils';
import { toast } from '@/hooks/use-toast';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from './ui/alert-dialog';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from './ui/card';
import { ScrollArea } from './ui/scroll-area';

interface VoucherDetailsModalProps {
  voucher: Voucher;
  onClose: () => void;
  showDelete?: boolean;
  onDelete?: (voucherId: string) => void;
  showAddBack?: boolean;
  department?: string;
}

export function VoucherDetailsModal({
  voucher,
  onClose,
  showDelete = false,
  onDelete,
  showAddBack = false,
  department
}: VoucherDetailsModalProps) {
  console.log('Rendering VoucherDetailsModal for voucher:', voucher.voucherId, 'in department:', department);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const updateVoucher = useAppStore((state) => state.updateVoucher);

  useEffect(() => {
    const handleDialogClose = () => {
      onClose();
    };

    document.addEventListener('dialog-close', handleDialogClose);
    return () => {
      document.removeEventListener('dialog-close', handleDialogClose);
    };
  }, [onClose]);

  const formatAmount = (amount: number | string | undefined) => {
    if (amount === undefined || amount === null) {
      return `0.00 ${voucher.currency}`;
    }
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    if (isNaN(numAmount)) {
      return `0.00 ${voucher.currency}`;
    }
    return `${numAmount.toFixed(2)} ${voucher.currency}`;
  };

  const formatStatus = (status: string) => {
    switch (status) {
      case 'VOUCHER CERTIFIED':
        return <Badge variant="success">CERTIFIED</Badge>;
      case 'VOUCHER REJECTED':
        return <Badge variant="destructive">REJECTED</Badge>;
      case 'AUDIT: PROCESSING':
        return <Badge variant="warning">PROCESSING</Badge>;
      case 'PENDING SUBMISSION':
        return <Badge variant="outline">PENDING SUBMISSION</Badge>;
      case 'PENDING RECEIPT':
        return <Badge variant="outline">PENDING RECEIPT</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const handleAddBack = () => {
    try {
      console.log(`Adding voucher ${voucher.id} (${voucher.voucherId}) back to pending submission`);

      // APPROACH 1: Preserve the original voucher ID when adding rejected vouchers back to PENDING
      // This maintains traceability and history of the voucher throughout its lifecycle
      // The voucher ID remains in the blacklist for record-keeping, but this doesn't prevent resubmission

      // Update the voucher to move it back to pending submission while preserving its ID
      // Determine the appropriate comment based on the voucher's current status
      const currentDate = new Date().toLocaleDateString();
      const commentText = voucher.status === "VOUCHER REJECTED"
        ? `Re-added from rejection on ${currentDate}`
        : voucher.isReturned || voucher.status === "VOUCHER RETURNED"
          ? `Re-added from returned on ${currentDate}`
          : `Re-added to pending on ${currentDate}`;

      updateVoucher(voucher.id, {
        status: "PENDING SUBMISSION",
        sentToAudit: false,
        isReturned: false,
        returnTime: undefined,
        returnComment: undefined,
        rejectionTime: undefined,
        comment: commentText,
        deleted: false, // Make sure it's not marked as deleted
        // Reset any fields that might prevent it from appearing in the PROCESSING tab
        auditDispatchTime: undefined,
        auditDispatchedBy: undefined,
        dispatched: false,
        dispatchTime: undefined,
        dispatchedBy: undefined,
        pendingReturn: false
      });

      // Create a toast message that reflects the source of the re-addition
      const toastMessage = voucher.status === "VOUCHER REJECTED"
        ? `Voucher ${voucher.voucherId} re-added from rejection to Pending Submission`
        : voucher.isReturned || voucher.status === "VOUCHER RETURNED"
          ? `Voucher ${voucher.voucherId} re-added from returned to Pending Submission`
          : `Voucher ${voucher.voucherId} moved back to Pending Submission`;

      toast({
        title: "Success",
        description: toastMessage,
        duration: 3000,
      });

      onClose();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add voucher back",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete(voucher.id);
    }
  };

  return (
    <>
      <CustomDialog onClose={onClose}>
        <CustomDialogContent className="max-w-3xl max-h-[90vh] flex flex-col">
          <CustomDialogHeader className="pr-8">
            <CustomDialogTitle className="uppercase">Voucher Details: {voucher.voucherId}</CustomDialogTitle>
            <CustomDialogDescription>
              Viewing details for voucher created on {formatVMSDateTime(voucher.date)}
            </CustomDialogDescription>
          </CustomDialogHeader>

          <Tabs defaultValue="details" className="flex-1 overflow-hidden flex flex-col">
            <TabsList className="grid grid-cols-2">
              <TabsTrigger value="details">Voucher Details</TabsTrigger>
              <TabsTrigger value="tracking">Tracking Information</TabsTrigger>
            </TabsList>

            <ScrollArea className="flex-1">
              <TabsContent value="details" className="space-y-4 p-1">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-semibold uppercase">Claimant</h3>
                    <p className="text-sm uppercase">{voucher.claimant}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-semibold uppercase">Department</h3>
                    <p className="text-sm uppercase">{voucher.department}</p>
                  </div>
                </div>

                <Separator />

                <div>
                  <h3 className="text-sm font-semibold uppercase">Description</h3>
                  <p className="text-sm uppercase">{voucher.description}</p>
                </div>

                <Separator />

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-semibold uppercase">Amount</h3>
                    <p className="text-sm uppercase">{formatAmount(voucher.amount)}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-semibold uppercase">Status</h3>
                    <div className="mt-1">{formatStatus(voucher.status)}</div>
                  </div>
                </div>

                {voucher.preAuditedAmount && (
                  <>
                    <Separator />
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <h3 className="text-sm font-semibold uppercase">Certified Amount</h3>
                        <p className="text-sm uppercase">{formatAmount(voucher.preAuditedAmount)}</p>
                      </div>
                      {voucher.preAuditedBy && (
                        <div>
                          <h3 className="text-sm font-semibold uppercase">Certified By</h3>
                          <p className="text-sm uppercase">{voucher.preAuditedBy}</p>
                        </div>
                      )}
                    </div>
                  </>
                )}

                {voucher.taxType && (
                  <>
                    <Separator />
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <h3 className="text-sm font-semibold uppercase">Tax Type</h3>
                        <p className="text-sm uppercase">{voucher.taxType}</p>
                      </div>
                      {voucher.taxAmount && (
                        <div>
                          <h3 className="text-sm font-semibold uppercase">Tax Amount</h3>
                          <p className="text-sm uppercase">{formatAmount(voucher.taxAmount)}</p>
                        </div>
                      )}
                    </div>
                  </>
                )}

                {voucher.comment && (
                  <>
                    <Separator />
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm uppercase">Comment</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm uppercase">{voucher.comment}</p>
                      </CardContent>
                    </Card>
                  </>
                )}
              </TabsContent>

              <TabsContent value="tracking" className="space-y-4 p-1">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm uppercase">Voucher Creation</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <p className="text-xs font-medium uppercase">Date Created</p>
                        <p className="text-sm uppercase">{formatVMSDateTime(voucher.date)}</p>
                      </div>
                      <div>
                        <p className="text-xs font-medium uppercase">Created By</p>
                        <p className="text-sm uppercase">{voucher.createdBy || "N/A"}</p>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <p className="text-xs font-medium uppercase">Department</p>
                        <p className="text-sm uppercase">{voucher.department}</p>
                      </div>
                      <div>
                        <p className="text-xs font-medium uppercase">Original Department</p>
                        <p className="text-sm uppercase">{voucher.originalDepartment || voucher.department}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* ENHANCED: Show offset/reference tracking information */}
                {voucher.referenceId && (
                  <Card className="border-blue-200 bg-blue-50 dark:bg-blue-950 dark:border-blue-800">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm uppercase text-blue-700 dark:text-blue-300">
                        Offset Tracking Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <p className="text-xs font-medium uppercase">Reference Voucher ID</p>
                          <p className="text-sm uppercase font-semibold text-blue-600 dark:text-blue-400">
                            {voucher.referenceId}
                          </p>
                        </div>
                        <div>
                          <p className="text-xs font-medium uppercase">Workflow Type</p>
                          <p className="text-sm uppercase font-semibold text-blue-600 dark:text-blue-400">
                            PROCESSED BY AUDIT
                          </p>
                        </div>
                      </div>
                      <div className="mt-2 p-2 bg-blue-100 dark:bg-blue-900 rounded">
                        <p className="text-xs text-blue-700 dark:text-blue-300">
                          ℹ️ This voucher was processed by audit and has replaced the original voucher ({voucher.referenceId})
                          in the workflow. The original voucher has been offset and removed from the Processing tab.
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* ENHANCED: Workflow Status Timeline */}
                <Card className="border-green-200 bg-green-50 dark:bg-green-950 dark:border-green-800">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm uppercase text-green-700 dark:text-green-300">
                      Workflow Status & Timeline
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <p className="text-xs font-medium uppercase">Current Status</p>
                        <p className="text-sm uppercase font-semibold text-green-600 dark:text-green-400">
                          {voucher.status}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs font-medium uppercase">Workflow Stage</p>
                        <p className="text-sm uppercase font-semibold text-green-600 dark:text-green-400">
                          {voucher.status === 'VOUCHER CERTIFIED' ? 'COMPLETED - CERTIFIED' :
                           voucher.status === 'VOUCHER REJECTED' ? 'COMPLETED - REJECTED' :
                           voucher.status === 'VOUCHER PROCESSING' ? 'DEPARTMENT PROCESSING' :
                           voucher.status === 'AUDIT: PROCESSING' ? 'AUDIT PROCESSING' :
                           voucher.status === 'PENDING RECEIPT' ? 'SENT TO AUDIT' :
                           voucher.referenceId ? 'PROCESSED BY AUDIT' : 'IN PROGRESS'}
                        </p>
                      </div>
                    </div>

                    {/* Show offset information if this is a processed voucher */}
                    {voucher.referenceId && (
                      <div className="mt-2 p-2 bg-green-100 dark:bg-green-900 rounded">
                        <p className="text-xs text-green-700 dark:text-green-300">
                          ✅ This voucher has completed the audit process and replaced the original voucher in the workflow.
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm uppercase">Department Dispatch</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <p className="text-xs font-medium uppercase">Dispatched By</p>
                        <p className="text-sm uppercase font-semibold">
                          {voucher.dispatchToAuditBy || voucher.dispatchedBy || "NOT YET DISPATCHED"}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs font-medium uppercase">Dispatch Time</p>
                        <p className="text-sm uppercase font-semibold">
                          {voucher.dispatchTime ? formatVMSDateTime(voucher.dispatchTime) : "NOT YET DISPATCHED"}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {voucher.receiptTime && (
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm uppercase">Audit Receipt</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <p className="text-xs font-medium uppercase">Received By</p>
                          <p className="text-sm uppercase">{voucher.receivedBy}</p>
                        </div>
                        <div>
                          <p className="text-xs font-medium uppercase">Receipt Time</p>
                          <p className="text-sm uppercase">{formatVMSDateTime(voucher.receiptTime)}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {voucher.status === "VOUCHER REJECTED" && (
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm uppercase">Rejection Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <p className="text-xs font-medium uppercase">Created By</p>
                          <p className="text-sm uppercase">{voucher.createdBy || "N/A"}</p>
                        </div>
                        <div>
                          <p className="text-xs font-medium uppercase">Dispatched By</p>
                          <p className="text-sm uppercase">{voucher.dispatchedBy || "N/A"}</p>
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <p className="text-xs font-medium uppercase">Rejected By</p>
                          <p className="text-sm uppercase">{voucher.rejectedBy || "N/A"}</p>
                        </div>
                        <div>
                          <p className="text-xs font-medium uppercase">Rejection Time</p>
                          <p className="text-sm uppercase">
                            {voucher.rejectionTime ? formatVMSDateTime(voucher.rejectionTime) : "N/A"}
                          </p>
                        </div>
                      </div>
                      {voucher.comment && (
                        <div>
                          <p className="text-xs font-medium uppercase">Rejection Reason</p>
                          <p className="text-sm uppercase">{voucher.comment}</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}

                {voucher.preAuditedBy && (
                  <Card className="border-purple-200 bg-purple-50 dark:bg-purple-950 dark:border-purple-800">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm uppercase text-purple-700 dark:text-purple-300">
                        Audit Processing & Certification
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <p className="text-xs font-medium uppercase">Processed By</p>
                          <p className="text-sm uppercase font-semibold text-purple-600 dark:text-purple-400">
                            {voucher.preAuditedBy}
                          </p>
                        </div>
                        <div>
                          <p className="text-xs font-medium uppercase">Certified Amount</p>
                          <p className="text-sm uppercase font-semibold text-purple-600 dark:text-purple-400">
                            {voucher.preAuditedAmount ? formatAmount(voucher.preAuditedAmount) : "N/A"}
                          </p>
                        </div>
                      </div>

                      {/* Show original amount comparison if different */}
                      {voucher.preAuditedAmount && voucher.amount && voucher.preAuditedAmount !== voucher.amount && (
                        <div className="mt-2 p-2 bg-purple-100 dark:bg-purple-900 rounded">
                          <p className="text-xs text-purple-700 dark:text-purple-300">
                            📊 Amount adjusted during audit: {formatAmount(voucher.amount)} → {formatAmount(voucher.preAuditedAmount)}
                          </p>
                        </div>
                      )}

                      {/* Show audit comments if available */}
                      {voucher.comment && (
                        <div className="mt-2">
                          <p className="text-xs font-medium uppercase">Audit Comments</p>
                          <p className="text-sm bg-purple-100 dark:bg-purple-900 p-2 rounded">
                            {voucher.comment}
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}

                {voucher.auditDispatchedBy && (
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm uppercase">Audit Dispatch</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <p className="text-xs font-medium uppercase">Dispatched By</p>
                          <p className="text-sm uppercase">{voucher.auditDispatchedBy}</p>
                        </div>
                        <div>
                          <p className="text-xs font-medium uppercase">Dispatch Time</p>
                          <p className="text-sm uppercase">{formatVMSDateTime(voucher.auditDispatchTime)}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {voucher.departmentReceivedBy && (
                  <Card className="border-teal-200 bg-teal-50 dark:bg-teal-950 dark:border-teal-800">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm uppercase text-teal-700 dark:text-teal-300">
                        Final Department Receipt
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <p className="text-xs font-medium uppercase">Received By</p>
                          <p className="text-sm uppercase font-semibold text-teal-600 dark:text-teal-400">
                            {voucher.departmentReceivedBy}
                          </p>
                        </div>
                        <div>
                          <p className="text-xs font-medium uppercase">Receipt Time</p>
                          <p className="text-sm uppercase font-semibold text-teal-600 dark:text-teal-400">
                            {formatVMSDateTime(voucher.departmentReceiptTime)}
                          </p>
                        </div>
                      </div>

                      {/* Show workflow completion status */}
                      <div className="mt-2 p-2 bg-teal-100 dark:bg-teal-900 rounded">
                        <p className="text-xs text-teal-700 dark:text-teal-300">
                          🎯 Voucher workflow completed. This voucher has been successfully processed through the audit system
                          {voucher.referenceId ? ` and has replaced the original voucher (${voucher.referenceId}) in the department's records.` : '.'}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>
            </ScrollArea>
          </Tabs>

          <CustomDialogFooter className="flex items-center justify-between gap-2">
            {showAddBack && (
              <Button variant="secondary" onClick={handleAddBack}>
                Add Back to Pending
              </Button>
            )}
            {showDelete && (
              <Button variant="destructive" onClick={() => setIsDeleteDialogOpen(true)}>
                Delete Voucher
              </Button>
            )}
            <Button
              variant="outline"
              onClick={() => {
                onClose();
              }}
            >
              Close
            </Button>
          </CustomDialogFooter>
        </CustomDialogContent>
      </CustomDialog>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={(open) => setIsDeleteDialogOpen(open)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to delete this voucher?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the voucher from the system.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-red-600 hover:bg-red-700">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
