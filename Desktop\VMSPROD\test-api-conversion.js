const axios = require('axios');

async function testAPIConversion() {
  try {
    console.log('🔄 Testing API Data Conversion...');

    // Login first
    const loginResponse = await axios.post('http://localhost:8080/api/auth/login', {
      department: 'AUDIT',
      username: 'SAMUEL ASIEDU',
      password: '123'
    }, {
      withCredentials: true
    });

    const cookies = loginResponse.headers['set-cookie'];
    const cookieHeader = cookies ? cookies.join('; ') : '';

    // Test GET endpoint
    console.log('\n📊 Testing GET /api/provisional-cash...');
    const getResponse = await axios.get('http://localhost:8080/api/provisional-cash', {
      headers: {
        'Cookie': cookieHeader
      }
    });

    console.log('Response status:', getResponse.status);
    console.log('Response headers:', getResponse.headers['content-type']);
    console.log('Raw response data:');
    console.log(JSON.stringify(getResponse.data, null, 2));

    // Find ADDY SABAH record
    const addyRecord = getResponse.data.find(record => 
      record.claimant && record.claimant.includes('ADDY SABAH')
    );

    if (addyRecord) {
      console.log('\n🎯 ADDY SABAH record from API:');
      console.log('ID:', addyRecord.id);
      console.log('Voucher Ref:', addyRecord.voucherRef);
      console.log('Claimant:', addyRecord.claimant);
      console.log('Main Amount:', addyRecord.mainAmount);
      console.log('Currency:', addyRecord.currency);
      console.log('Amount Retired:', addyRecord.amountRetired);
      console.log('Clearance Remark:', addyRecord.clearanceRemark);
      console.log('Date Retired:', addyRecord.dateRetired);
      console.log('Cleared By:', addyRecord.clearedBy);
      console.log('Comment:', addyRecord.comment);
    } else {
      console.log('❌ ADDY SABAH record not found in API response');
    }

  } catch (error) {
    console.error('❌ API Test Error:');
    console.error('Status:', error.response?.status);
    console.error('Data:', error.response?.data);
    console.error('Message:', error.message);
  }
}

testAPIConversion();
